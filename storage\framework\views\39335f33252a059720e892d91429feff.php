<?php echo $__env->make('components.head', ['titre' => 'Recherche Globale de Documents'], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<body>

    <!-- Main Wrapper -->
    <div class="main-wrapper">

        <?php echo $__env->make('components.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <?php echo $__env->make('components.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <!-- Page Wrapper -->
        <div class="page-wrapper">
            <div class="content">
                <div class="page-header">
                    <div class="add-item d-flex">
                        <div class="page-title">
                            <h4>Recherche Globale de Documents</h4>
                            <h6>Rechercher dans tous les documents des fonctionnaires</h6>
                        </div>
                    </div>
                </div>

                <!-- Search Form -->
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <form id="searchForm">
                                    <div class="row">
                                        <div class="col-lg-4 col-sm-6 col-12">
                                            <div class="input-blocks">
                                                <label>Terme de recherche</label>
                                                <input type="text" id="searchQuery" name="query"
                                                       placeholder="Nom, prénom, matricule..."
                                                       class="form-control" required>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6 col-12">
                                            <div class="input-blocks">
                                                <label>Type de document</label>
                                                <select id="documentType" name="document_type" class="form-control">
                                                    <option value="all">Tous les documents</option>
                                                    <option value="conge">Congés</option>
                                                    <option value="certificate">Certificats</option>
                                                    <option value="attestation">Attestations</option>
                                                    <option value="mutation">Mutations</option>
                                                    <option value="position">Positions</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-lg-2 col-sm-6 col-12">
                                            <div class="input-blocks">
                                                <label>&nbsp;</label>
                                                <button type="submit" class="btn btn-primary btn-block">
                                                    <i class="fa fa-search"></i> Rechercher
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-sm-6 col-12">
                                            <div class="input-blocks">
                                                <label>&nbsp;</label>
                                                <a href="<?php echo e(route('documents.export-conges-form')); ?>"
                                                   class="btn btn-success btn-block">
                                                    <i class="fa fa-download"></i> Export Congés
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loading Indicator -->
                <div id="loadingIndicator" class="text-center" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="sr-only">Recherche en cours...</span>
                    </div>
                    <p class="mt-2">Recherche en cours...</p>
                </div>

                <!-- Search Results -->
                <div id="searchResults" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fa fa-file-text"></i> Résultats de la recherche
                                <span id="resultsCount" class="badge badge-primary ml-2">0</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="resultsContainer">
                                <!-- Results will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- No Results Message -->
                <div id="noResults" class="card" style="display: none;">
                    <div class="card-body text-center">
                        <i class="fa fa-search fa-3x text-muted mb-3"></i>
                        <h5>Aucun résultat trouvé</h5>
                        <p class="text-muted">Essayez avec d'autres termes de recherche ou changez le type de document.</p>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row mt-4">
                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">
                                    <i class="fa fa-bolt"></i> Actions Rapides
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-lg-3 col-md-6 col-sm-12 mb-3">
                                        <div class="quick-action-card">
                                            <div class="card border-left-primary">
                                                <div class="card-body">
                                                    <h6><i class="fa fa-calendar text-primary"></i> Congés</h6>
                                                    <p class="text-muted small">Rechercher les décisions de congé</p>
                                                    <button class="btn btn-sm btn-outline-primary"
                                                            onclick="setDocumentType('conge')">
                                                        Rechercher
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 col-sm-12 mb-3">
                                        <div class="quick-action-card">
                                            <div class="card border-left-success">
                                                <div class="card-body">
                                                    <h6><i class="fa fa-certificate text-success"></i> Certificats</h6>
                                                    <p class="text-muted small">Rechercher les certificats</p>
                                                    <button class="btn btn-sm btn-outline-success"
                                                            onclick="setDocumentType('certificate')">
                                                        Rechercher
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 col-sm-12 mb-3">
                                        <div class="quick-action-card">
                                            <div class="card border-left-info">
                                                <div class="card-body">
                                                    <h6><i class="fa fa-file-text text-info"></i> Attestations</h6>
                                                    <p class="text-muted small">Générer attestations de travail</p>
                                                    <button class="btn btn-sm btn-outline-info"
                                                            onclick="setDocumentType('attestation')">
                                                        Rechercher
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-md-6 col-sm-12 mb-3">
                                        <div class="quick-action-card">
                                            <div class="card border-left-warning">
                                                <div class="card-body">
                                                    <h6><i class="fa fa-exchange text-warning"></i> Mutations</h6>
                                                    <p class="text-muted small">Consulter notes de mutation</p>
                                                    <button class="btn btn-sm btn-outline-warning"
                                                            onclick="setDocumentType('mutation')">
                                                        Rechercher
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /Page Wrapper -->
    </div>
    <!-- /Main Wrapper -->

    <!-- Form Modal for Congé Decision -->
    <div class="modal fade" id="congeDecisionModal" tabindex="-1" aria-labelledby="congeDecisionModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="congeDecisionModalLabel">Sélectionner les ampliations</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="congeDecisionForm" method="POST" target="_blank">
                    <?php echo csrf_field(); ?>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Veuillez sélectionner les ampliations à inclure dans le document :</label>
                            <div class="checkbox-group">
                                <div class="form-check">
                                    <input type="checkbox" name="ampliations[]" value="M. Medecin Chef du SRES de Fes" class="form-check-input" id="amp1">
                                    <label class="form-check-label" for="amp1">M. Medecin Chef du SRES de Fes</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" name="ampliations[]" value="M. directeur CHR alghassani Fes" class="form-check-input" id="amp2">
                                    <label class="form-check-label" for="amp2">M. directeur CHR alghassani Fes</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" name="ampliations[]" value="M. directeur Hopital ibn khatib" class="form-check-input" id="amp3">
                                    <label class="form-check-label" for="amp3">M. directeur Hopital ibn khatib</label>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" name="ampliations[]" value="M. directeur Hopital ibn baytar" class="form-check-input" id="amp4">
                                    <label class="form-check-label" for="amp4">M. directeur Hopital ibn baytar</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-primary">Générer PDF</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <?php echo $__env->make('components.foot', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <script>
        $(document).ready(function() {
            // Search form submission
            $('#searchForm').on('submit', function(e) {
                e.preventDefault();
                performSearch();
            });

            // Real-time search on input change (with debounce)
            let searchTimeout;
            $('#searchQuery').on('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    if ($('#searchQuery').val().length >= 2) {
                        performSearch();
                    }
                }, 500);
            });
        });

        function performSearch() {
            const query = $('#searchQuery').val();
            const documentType = $('#documentType').val();

            if (query.length < 2) {
                $('#searchResults, #noResults').hide();
                return;
            }

            // Show loading
            $('#loadingIndicator').show();
            $('#searchResults, #noResults').hide();

            // Perform AJAX search
            $.ajax({
                url: '<?php echo e(route("documents.search.post")); ?>',
                method: 'POST',
                data: {
                    query: query,
                    document_type: documentType,
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(response) {
                    $('#loadingIndicator').hide();

                    if (response.success && response.results.length > 0) {
                        displayResults(response.results);
                        $('#searchResults').show();
                        $('#noResults').hide();
                    } else {
                        $('#searchResults').hide();
                        $('#noResults').show();
                    }
                },
                error: function(xhr, status, error) {
                    $('#loadingIndicator').hide();
                    alert('Erreur lors de la recherche. Veuillez réessayer.');
                }
            });
        }

        function displayResults(results) {
            const container = $('#resultsContainer');
            container.empty();
            $('#resultsCount').text(results.length);

            results.forEach(function(result) {
                const resultHtml = `
                    <div class="result-item border-bottom pb-3 mb-3">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="mb-1">
                                    <span class="badge badge-${getTypeBadgeColor(result.type)} mr-2">${result.type}</span>
                                    ${result.title}
                                </h6>
                                <p class="text-muted mb-1">${result.description}</p>
                                <small class="text-muted">
                                    <i class="fa fa-user"></i> ${result.fonctionnaire}
                                    <i class="fa fa-id-card ml-2"></i> ${result.matricule}
                                    <i class="fa fa-calendar ml-2"></i> ${result.date}
                                </small>
                            </div>
                            <div class="col-md-4 text-right">
                                ${getActionButton(result)}
                            </div>
                        </div>
                    </div>
                `;
                container.append(resultHtml);
            });
        }

        function getTypeBadgeColor(type) {
            const colors = {
                'Congé': 'primary',
                'Certificat': 'success',
                'Attestation': 'info',
                'Mutation': 'warning',
                'Position': 'secondary'
            };
            return colors[type] || 'secondary';
        }

        function getActionButton(result) {
            if (result.requires_form && result.form_type === 'conge_decision') {
                return `<button class="btn btn-primary btn-sm" onclick="showCongeDecisionModal('${result.url}')">
                            <i class="fa fa-file-pdf-o"></i> Générer PDF
                        </button>`;
            } else {
                return `<a href="${result.url}" target="_blank" class="btn btn-primary btn-sm">
                            <i class="fa fa-eye"></i> Voir
                        </a>`;
            }
        }

        function showCongeDecisionModal(url) {
            $('#congeDecisionForm').attr('action', url);
            const modal = new bootstrap.Modal(document.getElementById('congeDecisionModal'));
            modal.show();
        }

        function setDocumentType(type) {
            $('#documentType').val(type);
            if ($('#searchQuery').val().length >= 2) {
                performSearch();
            }
        }
    </script>

    <style>
        .border-left-primary { border-left: 4px solid #007bff !important; }
        .border-left-success { border-left: 4px solid #28a745 !important; }
        .border-left-info { border-left: 4px solid #17a2b8 !important; }
        .border-left-warning { border-left: 4px solid #ffc107 !important; }

        .quick-action-card .card {
            transition: transform 0.2s;
        }

        .quick-action-card .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .result-item:last-child {
            border-bottom: none !important;
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }
    </style>
</body>
</html>
<?php /**PATH E:\apprh12062025\resources\views/documents/search.blade.php ENDPATH**/ ?>