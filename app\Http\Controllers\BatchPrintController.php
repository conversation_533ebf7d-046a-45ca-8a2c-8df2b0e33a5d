<?php

namespace App\Http\Controllers;

use App\Models\Fonctionnaire;
use App\Models\TypeConge;
use App\Models\CongeDecision;
use App\Utils\CustomPDF;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class BatchPrintController extends Controller
{
    /**
     * Show the batch print page
     */
    public function index()
    {
        $fonctionnaires = Fonctionnaire::with(['nomFormationSanitaire', 'service', 'fonction', 'specialiteGrade'])
            ->orderBy('nom')
            ->orderBy('prenom')
            ->get();

        $typeConges = TypeConge::all();

        return view('batch-print.index', compact('fonctionnaires', 'typeConges'));
    }

    /**
     * Generate batch attestations PDF
     */
    public function batchAttestations(Request $request)
    {
        $request->validate([
            'fonctionnaire_ids' => 'required|array|min:1',
            'fonctionnaire_ids.*' => 'exists:fonctionnaires,id'
        ]);

        $fonctionnaires = Fonctionnaire::whereIn('id', $request->fonctionnaire_ids)
            ->with(['nomFormationSanitaire', 'service', 'fonction', 'specialiteGrade'])
            ->get();

        $pdf = new CustomPDF();

        // Set document information
        $pdf->SetCreator('GRHDMSP-Fes');
        $pdf->SetAuthor('Med Kaddouri');
        $pdf->SetTitle('Attestations de Travail - Lot');
        $pdf->SetSubject('Export PDF');

        // Set header and footer images
        $pdf->headerImage = public_path('html/assets/img/pdf/header-delegation-urh.png');
        $pdf->footerImage = public_path('html/assets/img/pdf/footer-deligation.png');

        // Set margins
        $pdf->SetMargins(15, 50, 15);
        $pdf->SetHeaderMargin(20);
        $pdf->SetFooterMargin(20);
        $pdf->SetAutoPageBreak(true, 30);

        foreach ($fonctionnaires as $index => $fonctionnaire) {
            if ($index > 0) {
                $pdf->AddPage(); // New page for each attestation
            } else {
                $pdf->AddPage();
            }

            // Set font
            $pdf->SetFont('dejavusans', '', 12);

            // Title
            $pdf->Ln(20);
            $pdf->SetFont('dejavusans', 'B', 16);
            $pdf->Cell(0, 10, 'ATTESTATION DE TRAVAIL', 0, 1, 'C');
            $pdf->Ln(10);

            // Content
            $pdf->SetFont('dejavusans', '', 12);
            $pdf->Cell(0, 8, 'Je soussigné, Délégué Provincial de la Santé de Fès,', 0, 1, 'L');
            $pdf->Ln(5);
            $pdf->Cell(0, 8, 'Atteste que :', 0, 1, 'L');
            $pdf->Ln(5);

            // Employee details
            $pdf->SetFont('dejavusans', 'B', 12);
            $pdf->Cell(40, 8, 'Nom et Prénom :', 0, 0, 'L');
            $pdf->SetFont('dejavusans', '', 12);
            $pdf->Cell(0, 8, $fonctionnaire->nom . ' ' . $fonctionnaire->prenom, 0, 1, 'L');

            $pdf->SetFont('dejavusans', 'B', 12);
            $pdf->Cell(40, 8, 'PPR :', 0, 0, 'L');
            $pdf->SetFont('dejavusans', '', 12);
            $pdf->Cell(0, 8, $fonctionnaire->ppr, 0, 1, 'L');

            $pdf->SetFont('dejavusans', 'B', 12);
            $pdf->Cell(40, 8, 'Fonction :', 0, 0, 'L');
            $pdf->SetFont('dejavusans', '', 12);
            $pdf->Cell(0, 8, $fonctionnaire->fonction->nom ?? 'N/A', 0, 1, 'L');

            $pdf->SetFont('dejavusans', 'B', 12);
            $pdf->Cell(40, 8, 'Service :', 0, 0, 'L');
            $pdf->SetFont('dejavusans', '', 12);
            $pdf->Cell(0, 8, $fonctionnaire->service->nom ?? 'N/A', 0, 1, 'L');

            $pdf->SetFont('dejavusans', 'B', 12);
            $pdf->Cell(40, 8, 'Affectation :', 0, 0, 'L');
            $pdf->SetFont('dejavusans', '', 12);
            $pdf->Cell(0, 8, $fonctionnaire->nomFormationSanitaire->nom ?? 'N/A', 0, 1, 'L');

            $pdf->Ln(10);
            $pdf->SetFont('dejavusans', '', 12);
            $pdf->Cell(0, 8, 'Travaille effectivement au sein de notre délégation.', 0, 1, 'L');
            $pdf->Ln(5);
            $pdf->Cell(0, 8, 'Cette attestation est délivrée à l\'intéressé(e) pour servir et valoir ce que de droit.', 0, 1, 'L');

            // Date and signature
            $pdf->Ln(20);
            $pdf->Cell(0, 8, 'Fait à Fès, le ' . date('d/m/Y'), 0, 1, 'R');
            $pdf->Ln(10);
            $pdf->Cell(0, 8, 'Le Délégué Provincial', 0, 1, 'R');
        }

        // Output PDF
        return response()->streamDownload(
            fn() => $pdf->Output('attestations_lot_' . date('Y-m-d') . '.pdf', 'I'),
            'attestations_lot_' . date('Y-m-d') . '.pdf'
        );
    }

    /**
     * Show congé decisions management page
     */
    public function congeDecisions()
    {
        $fonctionnaires = Fonctionnaire::with(['nomFormationSanitaire', 'service', 'fonction'])
            ->orderBy('nom')
            ->orderBy('prenom')
            ->get();

        $typeConges = TypeConge::all();

        // Get existing decisions
        $decisions = CongeDecision::with(['fonctionnaire', 'typeConge'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('batch-print.conge-decisions', compact('fonctionnaires', 'typeConges', 'decisions'));
    }

    /**
     * Store congé decision
     */
    public function storeCongeDecision(Request $request)
    {
        $request->validate([
            'fonctionnaire_id' => 'required|exists:fonctionnaires,id',
            'type_conge_id' => 'required|exists:type_conges,id',
            'nombre_jours' => 'required|integer|min:1',
            'date_debut' => 'required|date',
            'date_fin' => 'required|date|after_or_equal:date_debut',
            'date_decision' => 'required|date',
            'numero_decision' => 'required|string|max:255',
            'remarques' => 'nullable|string'
        ]);

        try {
            DB::beginTransaction();

            CongeDecision::create([
                'fonctionnaire_id' => $request->fonctionnaire_id,
                'type_conge_id' => $request->type_conge_id,
                'nombre_jours' => $request->nombre_jours,
                'date_debut' => $request->date_debut,
                'date_fin' => $request->date_fin,
                'date_decision' => $request->date_decision,
                'numero_decision' => $request->numero_decision,
                'remarques' => $request->remarques,
                'statut' => 'approuve'
            ]);

            DB::commit();
            return redirect()->back()->with('success', 'Décision de congé créée avec succès');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'Erreur lors de la création de la décision: ' . $e->getMessage());
        }
    }

    /**
     * Export congé decisions to Excel
     */
    public function exportCongeDecisions(Request $request)
    {
        $query = CongeDecision::with(['fonctionnaire.nomFormationSanitaire', 'fonctionnaire.service', 'typeConge']);

        // Apply filters if provided
        if ($request->filled('type_conge_id')) {
            $query->where('type_conge_id', $request->type_conge_id);
        }

        if ($request->filled('ppr')) {
            $query->whereHas('fonctionnaire', function($q) use ($request) {
                $q->where('ppr', 'LIKE', '%' . $request->ppr . '%');
            });
        }

        if ($request->filled('date_debut')) {
            $query->where('date_decision', '>=', $request->date_debut);
        }

        if ($request->filled('date_fin')) {
            $query->where('date_decision', '<=', $request->date_fin);
        }

        $decisions = $query->get();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Headers
        $headers = [
            'A1' => 'PPR',
            'B1' => 'Nom',
            'C1' => 'Prénom',
            'D1' => 'Affectation',
            'E1' => 'Formation Sanitaire',
            'F1' => 'Service',
            'G1' => 'Type de Congé',
            'H1' => 'Nombre de Jours',
            'I1' => 'Date Début',
            'J1' => 'Date Fin',
            'K1' => 'Date Décision',
            'L1' => 'Numéro Décision',
            'M1' => 'Statut',
            'N1' => 'Remarques'
        ];

        foreach ($headers as $cell => $header) {
            $sheet->setCellValue($cell, $header);
        }

        // Style headers
        $sheet->getStyle('A1:N1')->getFont()->setBold(true);
        $sheet->getStyle('A1:N1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setARGB('FFCCCCCC');

        // Data
        foreach ($decisions as $index => $decision) {
            $row = $index + 2;
            $sheet->setCellValue('A' . $row, $decision->fonctionnaire->ppr);
            $sheet->setCellValue('B' . $row, $decision->fonctionnaire->nom);
            $sheet->setCellValue('C' . $row, $decision->fonctionnaire->prenom);
            $sheet->setCellValue('D' . $row, $decision->fonctionnaire->nomFormationSanitaire->nom ?? 'N/A');
            $sheet->setCellValue('E' . $row, $decision->fonctionnaire->nomFormationSanitaire->nom ?? 'N/A');
            $sheet->setCellValue('F' . $row, $decision->fonctionnaire->service->nom ?? 'N/A');
            $sheet->setCellValue('G' . $row, $decision->typeConge->nom);
            $sheet->setCellValue('H' . $row, $decision->nombre_jours);
            $sheet->setCellValue('I' . $row, $decision->date_debut);
            $sheet->setCellValue('J' . $row, $decision->date_fin);
            $sheet->setCellValue('K' . $row, $decision->date_decision);
            $sheet->setCellValue('L' . $row, $decision->numero_decision);
            $sheet->setCellValue('M' . $row, ucfirst($decision->statut));
            $sheet->setCellValue('N' . $row, $decision->remarques);
        }

        // Auto-size columns
        foreach (range('A', 'N') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        $fileName = 'decisions_conge_' . date('Y-m-d') . '.xlsx';
        $filePath = storage_path("app/public/{$fileName}");

        $writer = new Xlsx($spreadsheet);
        $writer->save($filePath);

        return response()->download($filePath)->deleteFileAfterSend();
    }

    /**
     * Print single decision PDF
     */
    public function printSingleDecision($id)
    {
        $decision = CongeDecision::with(['fonctionnaire.specialiteGrade.grade.cadre', 'fonctionnaire.nomFormationSanitaire', 'typeConge'])->findOrFail($id);

        return $this->generateDecisionPDF([$decision], 'decision_conge_' . $decision->numero_decision . '.pdf');
    }

    /**
     * Print single decision PDF with ampliations
     */
    public function printSingleDecisionWithAmpliations($id, Request $request)
    {
        // Validate ampliations
        $request->validate([
            'ampliations' => 'nullable|array',
            'ampliations.*' => 'string',
        ]);

        $decision = CongeDecision::with(['fonctionnaire.specialiteGrade.grade.cadre', 'fonctionnaire.nomFormationSanitaire', 'typeConge'])->findOrFail($id);

        return $this->generateDecisionPDFWithAmpliations([$decision], 'decision_conge_' . $decision->numero_decision . '.pdf', $request->input('ampliations', []));
    }

    /**
     * Update ampliations for a decision
     */
    public function updateAmpliations($id, Request $request)
    {
        // Validate ampliations
        $request->validate([
            'ampliations' => 'nullable|array',
            'ampliations.*' => 'string',
        ]);

        $decision = CongeDecision::findOrFail($id);
        $decision->ampliations = $request->input('ampliations', []);
        $decision->save();

        if ($request->ajax()) {
            return response()->json(['success' => true, 'message' => 'Ampliations mises à jour avec succès.']);
        }

        return redirect()->back()->with('success', 'Ampliations mises à jour avec succès.');
    }

    /**
     * Print multiple decisions PDF with their individual ampliations
     */
    public function printDecisionsWithIndividualAmpliations(Request $request)
    {
        $request->validate([
            'decision_ids' => 'required|array|min:1',
            'decision_ids.*' => 'exists:conge_decisions,id'
        ]);

        $decisions = CongeDecision::with(['fonctionnaire.specialiteGrade.grade.cadre', 'fonctionnaire.nomFormationSanitaire', 'typeConge'])
            ->whereIn('id', $request->decision_ids)
            ->get();

        return $this->generateDecisionPDFWithIndividualAmpliations($decisions, 'decisions_conge_lot_' . date('Y-m-d') . '.pdf');
    }

    /**
     * Print multiple decisions PDF
     */
    public function printDecisions(Request $request)
    {
        $request->validate([
            'decision_ids' => 'required|array|min:1',
            'decision_ids.*' => 'exists:conge_decisions,id',
            'batch_ampliations' => 'nullable|array',
            'batch_ampliations.*' => 'string',
        ]);

        $decisions = CongeDecision::with(['fonctionnaire.specialiteGrade.grade.cadre', 'fonctionnaire.nomFormationSanitaire', 'typeConge'])
            ->whereIn('id', $request->decision_ids)
            ->get();

        // Check if batch ampliations are provided
        if ($request->has('batch_ampliations') && !empty($request->batch_ampliations)) {
            return $this->generateDecisionPDFWithAmpliations($decisions, 'decisions_conge_lot_' . date('Y-m-d') . '.pdf', $request->batch_ampliations);
        }

        return $this->generateDecisionPDF($decisions, 'decisions_conge_lot_' . date('Y-m-d') . '.pdf');
    }

    /**
     * Generate PDF for congé decisions
     */
    private function generateDecisionPDF($decisions, $filename)
    {
        $pdf = new CustomPDF();

        // Set document information
        $pdf->SetCreator('GRHDMSP-Fes');
        $pdf->SetAuthor('Med Kaddouri');
        $pdf->SetTitle('Décisions de Congé');
        $pdf->SetSubject('Décisions de Congé');

        // Set header and footer images
        $pdf->headerImage = public_path('html/assets/img/pdf/header-delegation-urh.png');
        $pdf->footerImage = public_path('html/assets/img/pdf/footer-deligation.png');

        // Set margins - adjusted for content
        $pdf->SetMargins(15, 45, 15);
        $pdf->SetHeaderMargin(15);
        $pdf->SetFooterMargin(25);
        $pdf->SetAutoPageBreak(true, 25); // Enable auto page break with footer margin

        foreach ($decisions as $index => $decision) {
            if ($index > 0) {
                $pdf->AddPage();
            } else {
                $pdf->AddPage();
            }

            // Set font
            $pdf->SetFont('dejavusans', '', 11);

            // Title - reduced spacing
            $pdf->Ln(5);
            $pdf->SetFont('dejavusans', 'B', 14);
            $pdf->Cell(0, 6, 'DÉCISION N° ' . $decision->numero_decision, 0, 1, 'C');
            $pdf->Ln(4);

            // Content - reduced spacing
            $pdf->SetFont('dejavusans', '', 11);
            $pdf->Cell(0, 6, 'Le Délégué du M.S à la Préfecture de Fès :', 0, 1, 'L');
            $pdf->Ln(3);

            $pdf->Cell(0, 6, 'Vu la demande de Congé présentée par ' . $decision->fonctionnaire->nom . ' ' . $decision->fonctionnaire->prenom, 0, 1, 'L');
            $pdf->Ln(2);

            $pdf->Cell(0, 6, 'Vu les dispositions du dahir du 24 février 1958 N° 1-58-008 portant réglementation', 0, 1, 'L');
            $pdf->Cell(0, 6, 'sur les congés du personnel des Administrations Publiques.', 0, 1, 'L');
            $pdf->Ln(2);

            $pdf->Cell(0, 6, 'Modifiées et complétées par la loi N° 59-44 Du 19 Mai 2011 (B.O°) et selon la', 0, 1, 'L');
            $pdf->Cell(0, 6, 'circulaire Ministérielle N° 01/13 du 01 Août 2011', 0, 1, 'L');
            $pdf->Ln(6);

            // DECIDE section
            $pdf->SetFont('dejavusans', 'B', 12);
            $pdf->Cell(0, 8, 'DÉCIDE :', 0, 1, 'C');
            $pdf->Ln(5);

            // Article unique avec format aligné
            $pdf->SetFont('dejavusans', 'B', 11);
            $pdf->Cell(0, 6, 'Article unique :', 0, 1, 'L');
            $pdf->Ln(3);

            // Format aligné avec colonnes
            $pdf->SetFont('dejavusans', '', 11);

            // Type de Congé
            $pdf->Cell(50, 6, 'Type de Congé', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->typeConge->nom, 0, 1, 'L');

            // Une durée de
            $pdf->Cell(50, 6, 'Une durée de', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->nombre_jours . ' jour(s)', 0, 1, 'L');

            // Au titre de l'année
            $pdf->Cell(50, 6, 'Au titre de l\'année', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->date_debut->format('Y'), 0, 1, 'L');

            // Accordé à
            $pdf->Cell(50, 6, 'Accordé à', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->fonctionnaire->nom . ' ' . $decision->fonctionnaire->prenom, 0, 1, 'L');

            // PPR
            $pdf->Cell(50, 6, 'PPR', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->fonctionnaire->ppr, 0, 1, 'L');

            // Grade
            if ($decision->fonctionnaire->specialiteGrade && $decision->fonctionnaire->specialiteGrade->grade) {
                $pdf->Cell(50, 6, 'Grade', 0, 0, 'L');
                $pdf->Cell(10, 6, ':', 0, 0, 'L');
                $pdf->Cell(0, 6, $decision->fonctionnaire->specialiteGrade->grade->nom, 0, 1, 'L');

                // Cadre
                if ($decision->fonctionnaire->specialiteGrade->grade->cadre) {
                    $pdf->Cell(50, 6, 'Cadre', 0, 0, 'L');
                    $pdf->Cell(10, 6, ':', 0, 0, 'L');
                    $pdf->Cell(0, 6, $decision->fonctionnaire->specialiteGrade->grade->cadre->nom, 0, 1, 'L');
                }

                // Spécialité
                $pdf->Cell(50, 6, 'Spécialité', 0, 0, 'L');
                $pdf->Cell(10, 6, ':', 0, 0, 'L');
                $pdf->Cell(0, 6, $decision->fonctionnaire->specialiteGrade->nom, 0, 1, 'L');
            }

            // Formation sanitaire
            if ($decision->fonctionnaire->nomFormationSanitaire) {
                $pdf->Cell(50, 6, 'Est en fonction', 0, 0, 'L');
                $pdf->Cell(10, 6, ':', 0, 0, 'L');
                $pdf->Cell(0, 6, $decision->fonctionnaire->nomFormationSanitaire->nom, 0, 1, 'L');
            }

            // Pour en bénéficier à compter du
            $pdf->Cell(50, 6, 'Pour en bénéficier à compter du', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, '  ' . $decision->date_debut->format('d/m/Y'), 0, 1, 'L');

            // Cadre
            $pdf->Cell(60, 6, '-Cadre', 0, 0, 'L');
            $pdf->Cell(20, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->fonctionnaire->specialiteGrade->grade->cadre->nom ?? 'N/A', 0, 1, 'L');

            // Grade-Cadre
            $pdf->Cell(60, 6, '-Grade', 0, 0, 'L');
            $pdf->Cell(20, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->fonctionnaire->specialiteGrade->grade->nom ?? 'N/A', 0, 1, 'L');

            // Est en fonction
            $pdf->Cell(60, 6, '-Est en fonction', 0, 0, 'L');
            $pdf->Cell(20, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->fonctionnaire->nomFormationSanitaire->nom ?? 'N/A', 0, 1, 'L');

            $pdf->Ln(5);

            // Pour en bénéficier
            $pdf->Cell(0, 6, 'Pour en bénéficier d\'un congé ' . $decision->typeConge->nom . ' à compter du  ' . $decision->date_debut->format('d/m/Y'), 0, 1, 'L');


            // Date and signature - aligned to right
            $pdf->Ln(8);
            $pdf->Cell(0, 5, 'Fès le : ' . date('d/m/Y'), 0, 1, 'R');
            $pdf->SetFont('dejavusans', 'B', 10);
            $pdf->Cell(0, 5, 'LE DÉLÉGUÉ DU MINISTRE DE LA SANTÉ', 0, 1, 'R');
            $pdf->Cell(0, 5, 'À LA PRÉFECTURE FÈS', 0, 1, 'R');

            $pdf->Ln(15); // Reduced space for signature

            // Ampilation - reduced spacing
            $pdf->SetFont('dejavusans', 'B', 11);
            $pdf->Cell(0, 6, 'Ampilation :', 0, 1, 'L');
            $pdf->SetFont('dejavusans', '', 11);
            $pdf->Cell(0, 6, '-L\'intéressé', 0, 1, 'L');
            $pdf->Cell(0, 6, '-Archive', 0, 1, 'L');
            $pdf->Cell(0, 6, '-M le chef affaire administratif et économique', 0, 1, 'L');
        }

        // Output PDF
        return response()->streamDownload(
            fn() => $pdf->Output($filename, 'I'),
            $filename
        );
    }

    /**
     * Generate PDF for congé decisions with custom ampliations
     */
    private function generateDecisionPDFWithAmpliations($decisions, $filename, $ampliations = [])
    {
        $pdf = new CustomPDF();

        // Set document information
        $pdf->SetCreator('GRHDMSP-Fes');
        $pdf->SetAuthor('Med Kaddouri');
        $pdf->SetTitle('Décisions de Congé');
        $pdf->SetSubject('Décisions de Congé');

        // Set header and footer images
        $pdf->headerImage = public_path('html/assets/img/pdf/header-delegation-urh.png');
        $pdf->footerImage = public_path('html/assets/img/pdf/footer-deligation.png');

        // Set margins - adjusted for content
        $pdf->SetMargins(15, 45, 15);
        $pdf->SetHeaderMargin(15);
        $pdf->SetFooterMargin(25);
        $pdf->SetAutoPageBreak(true, 25); // Enable auto page break with footer margin

        foreach ($decisions as $index => $decision) {
            if ($index > 0) {
                $pdf->AddPage();
            } else {
                $pdf->AddPage();
            }

            // Set font
            $pdf->SetFont('dejavusans', '', 11);

            // Title - reduced spacing
            $pdf->Ln(5);
            $pdf->SetFont('dejavusans', 'B', 14);
            $pdf->Cell(0, 6, 'DÉCISION N° ' . $decision->numero_decision, 0, 1, 'C');
            $pdf->Ln(4);

            // Content - reduced spacing
            $pdf->SetFont('dejavusans', '', 11);
            $pdf->Cell(0, 6, 'Le Délégué du M.S à la Préfecture de Fès :', 0, 1, 'L');
            $pdf->Ln(3);

            $pdf->Cell(0, 6, 'Vu la demande de Congé présentée par ' . $decision->fonctionnaire->nom . ' ' . $decision->fonctionnaire->prenom, 0, 1, 'L');
            $pdf->Ln(2);

            $pdf->Cell(0, 6, 'Vu les dispositions du dahir du 24 février 1958 N° 1-58-008 portant réglementation', 0, 1, 'L');
            $pdf->Cell(0, 6, 'sur les congés du personnel des Administrations Publiques.', 0, 1, 'L');
            $pdf->Ln(2);

            $pdf->Cell(0, 6, 'Vu l\'avis favorable de son chef immédiat', 0, 1, 'L');
            $pdf->Ln(5);

            // DECIDE section
            $pdf->SetFont('dejavusans', 'B', 12);
            $pdf->Cell(0, 8, 'DÉCIDE :', 0, 1, 'C');
            $pdf->Ln(5);

            // Article unique avec format aligné
            $pdf->SetFont('dejavusans', 'B', 11);
            $pdf->Cell(0, 6, 'Article unique :', 0, 1, 'L');
            $pdf->Ln(3);

            // Format aligné avec colonnes
            $pdf->SetFont('dejavusans', '', 11);

            // Type de Congé
            $pdf->Cell(50, 6, 'Type de Congé', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->typeConge->nom, 0, 1, 'L');

            // Une durée de
            $pdf->Cell(50, 6, 'Une durée de', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->nombre_jours . ' jour(s)', 0, 1, 'L');

            // Au titre de l'année
            $pdf->Cell(50, 6, 'Au titre de l\'année', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->date_debut->format('Y'), 0, 1, 'L');

            // Accordé à
            $pdf->Cell(50, 6, 'Accordé à', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->fonctionnaire->nom . ' ' . $decision->fonctionnaire->prenom, 0, 1, 'L');

            // PPR
            $pdf->Cell(50, 6, 'PPR', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->fonctionnaire->ppr, 0, 1, 'L');

            // Grade
            if ($decision->fonctionnaire->specialiteGrade && $decision->fonctionnaire->specialiteGrade->grade) {
                $pdf->Cell(50, 6, 'Grade', 0, 0, 'L');
                $pdf->Cell(10, 6, ':', 0, 0, 'L');
                $pdf->Cell(0, 6, $decision->fonctionnaire->specialiteGrade->grade->nom, 0, 1, 'L');

                // Cadre
                if ($decision->fonctionnaire->specialiteGrade->grade->cadre) {
                    $pdf->Cell(50, 6, 'Cadre', 0, 0, 'L');
                    $pdf->Cell(10, 6, ':', 0, 0, 'L');
                    $pdf->Cell(0, 6, $decision->fonctionnaire->specialiteGrade->grade->cadre->nom, 0, 1, 'L');
                }

                // Spécialité
                $pdf->Cell(50, 6, 'Spécialité', 0, 0, 'L');
                $pdf->Cell(10, 6, ':', 0, 0, 'L');
                $pdf->Cell(0, 6, $decision->fonctionnaire->specialiteGrade->nom, 0, 1, 'L');
            }

            // Formation sanitaire
            if ($decision->fonctionnaire->nomFormationSanitaire) {
                $pdf->Cell(50, 6, 'Est en fonction', 0, 0, 'L');
                $pdf->Cell(10, 6, ':', 0, 0, 'L');
                $pdf->Cell(0, 6, $decision->fonctionnaire->nomFormationSanitaire->nom, 0, 1, 'L');
            }

            // Pour en bénéficier à compter du

            $pdf->Cell(50, 6, 'Pour en bénéficier à compter du ', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, '  ' . $decision->date_debut->format('d/m/Y'), 0, 1, 'L');

            // Date and signature - aligned to right
            $pdf->Ln(8);
            $pdf->Cell(0, 5, 'Fès le : ' . date('d/m/Y'), 0, 1, 'R');
            $pdf->SetFont('dejavusans', 'B', 10);
            $pdf->Cell(0, 5, 'LE DÉLÉGUÉ DU MINISTRE DE LA SANTÉ', 0, 1, 'R');
            $pdf->Cell(0, 5, 'À LA PRÉFECTURE FÈS', 0, 1, 'R');

            $pdf->Ln(15); // Reduced space for signature

            // Ampilation with custom ampliations
            $pdf->SetFont('dejavusans', 'B', 11);
            $pdf->Cell(0, 6, 'Ampilation :', 0, 1, 'L');
            $pdf->SetFont('dejavusans', '', 11);
            $pdf->Cell(0, 6, '-L\'intéressé', 0, 1, 'L');
            $pdf->Cell(0, 6, '-Archive', 0, 1, 'L');

            // Add custom ampliations if provided
            if (!empty($ampliations)) {
                foreach ($ampliations as $ampliation) {
                    $pdf->Cell(0, 6, '-' . $ampliation, 0, 1, 'L');
                }
            } else {
                // Default ampliations if none selected
                $pdf->Cell(0, 6, '-M le chef affaire administratif et économique', 0, 1, 'L');
            }
        }

        // Output PDF
        return response()->streamDownload(
            fn() => $pdf->Output($filename, 'I'),
            $filename
        );
    }

    /**
     * Generate PDF for congé decisions with individual ampliations
     */
    private function generateDecisionPDFWithIndividualAmpliations($decisions, $filename)
    {
        $pdf = new CustomPDF();

        // Set document information
        $pdf->SetCreator('GRHDMSP-Fes');
        $pdf->SetAuthor('Med Kaddouri');
        $pdf->SetTitle('Décisions de Congé');
        $pdf->SetSubject('Décisions de Congé');

        // Set header and footer images
        $pdf->headerImage = public_path('html/assets/img/pdf/header-delegation-urh.png');
        $pdf->footerImage = public_path('html/assets/img/pdf/footer-deligation.png');

        // Set margins - adjusted for content
        $pdf->SetMargins(15, 45, 15);
        $pdf->SetHeaderMargin(15);
        $pdf->SetFooterMargin(15); // Reduced footer margin
        $pdf->SetAutoPageBreak(true, 15); // Reduced auto page break margin

        foreach ($decisions as $index => $decision) {
            if ($index > 0) {
                $pdf->AddPage();
            } else {
                $pdf->AddPage();
            }

            // Set font
            $pdf->SetFont('dejavusans', '', 11);

            // Title - reduced spacing
            $pdf->Ln(5);
            $pdf->SetFont('dejavusans', 'B', 14);
            $pdf->Cell(0, 6, 'DÉCISION N° ' . $decision->numero_decision, 0, 1, 'C');
            $pdf->Ln(4);

            // Content - reduced spacing
            $pdf->SetFont('dejavusans', '', 11);
            $pdf->Cell(0, 6, 'Le Délégué du M.S à la Préfecture de Fès :', 0, 1, 'L');
            $pdf->Ln(3);

            $pdf->Cell(0, 6, 'Vu la demande de Congé présentée par ' . $decision->fonctionnaire->nom . ' ' . $decision->fonctionnaire->prenom, 0, 1, 'L');
            $pdf->Ln(2);

            $pdf->Cell(0, 6, 'Vu les dispositions du dahir du 24 février 1958 N° 1-58-008 portant réglementation', 0, 1, 'L');
            $pdf->Cell(0, 6, 'sur les congés du personnel des Administrations Publiques.', 0, 1, 'L');
            $pdf->Ln(2);

            $pdf->Cell(0, 6, 'Vu l\'avis favorable de son chef immédiat', 0, 1, 'L');
            $pdf->Ln(5);

            // DECIDE section
            $pdf->SetFont('dejavusans', 'B', 12);
            $pdf->Cell(0, 8, 'DÉCIDE :', 0, 1, 'C');
            $pdf->Ln(5);

            // Article unique avec format aligné
            $pdf->SetFont('dejavusans', 'B', 11);
            $pdf->Cell(0, 6, 'Article unique :', 0, 1, 'L');
            $pdf->Ln(3);

            // Format aligné avec colonnes
            $pdf->SetFont('dejavusans', '', 11);

            // Type de Congé
            $pdf->Cell(50, 6, 'Type de Congé', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->typeConge->nom, 0, 1, 'L');

            // Une durée de
            $pdf->Cell(50, 6, 'Une durée de', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->nombre_jours . ' jour(s)', 0, 1, 'L');

            // Au titre de l'année
            $pdf->Cell(50, 6, 'Au titre de l\'année', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->date_debut->format('Y'), 0, 1, 'L');

            // Accordé à
            $pdf->Cell(50, 6, 'Accordé à', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->fonctionnaire->nom . ' ' . $decision->fonctionnaire->prenom, 0, 1, 'L');

            // PPR
            $pdf->Cell(50, 6, 'PPR', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, $decision->fonctionnaire->ppr, 0, 1, 'L');

            // Grade
            if ($decision->fonctionnaire->specialiteGrade && $decision->fonctionnaire->specialiteGrade->grade) {
                $pdf->Cell(50, 6, 'Grade', 0, 0, 'L');
                $pdf->Cell(10, 6, ':', 0, 0, 'L');
                $pdf->Cell(0, 6, $decision->fonctionnaire->specialiteGrade->grade->nom, 0, 1, 'L');

                // Cadre
                if ($decision->fonctionnaire->specialiteGrade->grade->cadre) {
                    $pdf->Cell(50, 6, 'Cadre', 0, 0, 'L');
                    $pdf->Cell(10, 6, ':', 0, 0, 'L');
                    $pdf->Cell(0, 6, $decision->fonctionnaire->specialiteGrade->grade->cadre->nom, 0, 1, 'L');
                }

                // Spécialité
                $pdf->Cell(50, 6, 'Spécialité', 0, 0, 'L');
                $pdf->Cell(10, 6, ':', 0, 0, 'L');
                $pdf->Cell(0, 6, $decision->fonctionnaire->specialiteGrade->nom, 0, 1, 'L');
            }

            // Formation sanitaire
            if ($decision->fonctionnaire->nomFormationSanitaire) {
                $pdf->Cell(50, 6, 'Est en fonction', 0, 0, 'L');
                $pdf->Cell(10, 6, ':', 0, 0, 'L');
                $pdf->Cell(0, 6, $decision->fonctionnaire->nomFormationSanitaire->nom, 0, 1, 'L');
            }

            // Pour en bénéficier à compter du
            $pdf->Cell(50, 6, 'Pour en bénéficier à compter du', 0, 0, 'L');
            $pdf->Cell(10, 6, ':', 0, 0, 'L');
            $pdf->Cell(0, 6, '  ' . $decision->date_debut->format('d/m/Y'), 0, 1, 'L');

            // Date and signature - aligned to right
            $pdf->Ln(8);
            $pdf->Cell(0, 5, 'Fès le : ' . date('d/m/Y'), 0, 1, 'R');
            $pdf->SetFont('dejavusans', 'B', 10);
            $pdf->Cell(0, 5, 'LE DÉLÉGUÉ DU MINISTRE DE LA SANTÉ', 0, 1, 'R');
            $pdf->Cell(0, 5, 'À LA PRÉFECTURE FÈS', 0, 1, 'R');

            // Calculate ampliations and position them at the bottom
            $ampliations = ['-L\'intéressé', '-Archive'];

            // Add individual ampliations for this decision
            if (!empty($decision->ampliations)) {
                foreach ($decision->ampliations as $ampliation) {
                    $ampliations[] = '-' . $ampliation;
                }
            } else {
                // Default ampliations if none configured for this decision
                $ampliations[] = '-M le chef affaire administratif et économique';
            }

            // Calculate total height needed for ampliations
            $ampliationHeight = 6; // Height per line
            $totalAmpliationHeight = (count($ampliations) + 1) * $ampliationHeight; // +1 for title

            // Get page dimensions
            $pageHeight = $pdf->getPageHeight();
            $footerMargin = 15; // Same as SetFooterMargin

            // Position ampliations directly above footer
            $ampliationY = $pageHeight - $footerMargin - $totalAmpliationHeight;
            $pdf->SetY($ampliationY);

            // Write ampliations
            $pdf->SetFont('dejavusans', 'B', 11);
            $pdf->Cell(0, 6, 'Ampilation :', 0, 1, 'L');
            $pdf->SetFont('dejavusans', '', 11);

            foreach ($ampliations as $ampliation) {
                $pdf->Cell(0, 6, $ampliation, 0, 1, 'L');
            }
        }

        // Output PDF
        return response()->streamDownload(
            fn() => $pdf->Output($filename, 'I'),
            $filename
        );
    }

    /**
     * Delete congé decision
     */
    public function deleteDecision($id)
    {
        try {
            $decision = CongeDecision::findOrFail($id);
            $decision->delete();

            return redirect()->back()->with('success', 'Décision supprimée avec succès');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Erreur lors de la suppression: ' . $e->getMessage());
        }
    }
}
