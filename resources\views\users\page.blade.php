<x-head titre="Gestion des utilisateurs" />
<x-header />
<x-sidebar />

<div class="page-wrapper">
    <style>
        .form-control .form-control-sm{
            display: none;
        }
    </style>
    <div class="content">
            <div class="page-header">
                <div class="add-item d-flex">
                    <div class="page-title">
                        <h4>Gestion des utilisateurs</h4>
                        <h6>Gérer les utilisateurs et leurs rôles</h6>
                    </div>
                </div>
                <ul class="table-top-head">
                    <li>
                        <a data-bs-toggle="tooltip" data-bs-placement="top" title="Actualiser" class="refresh-btn"
                            href="javascript:void(0);" onclick="refreshTable()"><i data-feather="rotate-ccw"
                                class="feather-rotate-ccw"></i></a>
                    </li>
                    <li>
                        <a data-bs-toggle="tooltip" data-bs-placement="top" title="Réduire" id="collapse-header"><i
                                data-feather="chevron-up" class="feather-chevron-up"></i></a>
                    </li>
                </ul>
            </div>

            <!-- Filter -->
            <div class="card table-list-card">
                <div class="card-header">
                    <div class="row">
                        <div class="col-lg-2">
                            <div class="form-group">
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                                    data-bs-target="#addUserModal">
                                    <i data-feather="plus-circle" class="me-1"></i> Ajouter Utilisateur
                                </button>
                            </div>
                        </div>
                        <div class="col-lg-2">
                            <div class="form-group">
                                <button type="button" class="btn btn-secondary" data-bs-toggle="modal"
                                    data-bs-target="#addRoleModal">
                                    <i data-feather="shield" class="me-1"></i> Ajouter Rôle
                                </button>
                            </div>
                        </div>
                        <div class="col-lg-8">
                            <div class="search-input">
                                <input type="text" id="searchInput" placeholder="Rechercher..." class="form-control"
                                    style="border: 2px solid #6571ff; font-size: 1.1rem;">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table" id="usersTable" style="width:100%">
                            <thead>
                                <tr>
                                    <th>Nom</th>
                                    <th>Email</th>
                                    <th>Rôles</th>
                                    <th>Date de création</th>
                                    <th class="no-sort">Actions</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Roles Table -->
            <div class="card table-list-card mt-4">
                <div class="card-header">
                    <h5>Gestion des Rôles</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table datanew" id="rolesTable">
                            <thead>
                                <tr>
                                    <th>Nom du Rôle</th>
                                    <th>Permissions</th>
                                    <th>Utilisateurs</th>
                                    <th class="no-sort">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($roles as $role)
                                <tr>
                                    <td>{{ $role->name }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ $role->permissions->count() }} permissions</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ $role->users->count() }} utilisateurs</span>
                                    </td>
                                    <td>
                                        <div class="action-table-data">
                                            <div class="edit-delete-action">
                                                <a class="me-2 p-2" href="#" onclick="editRole({{ $role->id }})">
                                                    <i data-feather="edit" class="feather-edit"></i>
                                                </a>
                                                <a class="confirm-text p-2" href="#" onclick="deleteRole({{ $role->id }})">
                                                    <i data-feather="trash-2" class="feather-trash-2"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ajouter un utilisateur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addUserForm">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nom</label>
                                <input type="text" name="name" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" name="email" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Mot de passe</label>
                                <input type="password" name="password" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Rôles</label>
                                <select name="roles[]" class="form-control" multiple>
                                    @foreach($roles as $role)
                                        <option value="{{ $role->name }}">{{ $role->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-cancel me-2" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-submit">Ajouter</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Modifier l'utilisateur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editUserForm">
                @csrf
                @method('PUT')
                <input type="hidden" id="editUserId" name="user_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nom</label>
                                <input type="text" name="name" id="editUserName" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" name="email" id="editUserEmail" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Nouveau mot de passe (optionnel)</label>
                                <input type="password" name="password" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Rôles</label>
                                <select name="roles[]" id="editUserRoles" class="form-control" multiple>
                                    @foreach($roles as $role)
                                        <option value="{{ $role->name }}">{{ $role->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-cancel me-2" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-submit">Mettre à jour</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Role Modal -->
<div class="modal fade" id="addRoleModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ajouter un rôle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addRoleForm">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">Nom du rôle</label>
                                <input type="text" name="name" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">Permissions</label>
                                <div class="row">
                                    @foreach($permissions as $group => $groupPermissions)
                                    <div class="col-md-4 mb-3">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">{{ ucfirst(str_replace('-', ' ', $group)) }}</h6>
                                                <div class="form-check">
                                                    <input class="form-check-input group-checkbox" type="checkbox"
                                                           data-group="{{ $group }}">
                                                    <label class="form-check-label">Tout sélectionner</label>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                @foreach($groupPermissions as $permission)
                                                <div class="form-check">
                                                    <input class="form-check-input permission-checkbox"
                                                           type="checkbox" name="permissions[]"
                                                           value="{{ $permission->name }}"
                                                           data-group="{{ $group }}">
                                                    <label class="form-check-label">
                                                        {{ ucfirst(str_replace(['-', '.'], [' ', ' '], $permission->name)) }}
                                                    </label>
                                                </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-cancel me-2" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-submit">Ajouter</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Role Modal -->
<div class="modal fade" id="editRoleModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Modifier le rôle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editRoleForm">
                @csrf
                @method('PUT')
                <input type="hidden" id="editRoleId" name="role_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">Nom du rôle</label>
                                <input type="text" name="name" id="editRoleName" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">Permissions</label>
                                <div class="row" id="editRolePermissions">
                                    @foreach($permissions as $group => $groupPermissions)
                                    <div class="col-md-4 mb-3">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">{{ ucfirst(str_replace('-', ' ', $group)) }}</h6>
                                                <div class="form-check">
                                                    <input class="form-check-input group-checkbox-edit" type="checkbox"
                                                           data-group="{{ $group }}">
                                                    <label class="form-check-label">Tout sélectionner</label>
                                                </div>
                                            </div>
                                            <div class="card-body">
                                                @foreach($groupPermissions as $permission)
                                                <div class="form-check">
                                                    <input class="form-check-input permission-checkbox-edit"
                                                           type="checkbox" name="permissions[]"
                                                           value="{{ $permission->name }}"
                                                           data-group="{{ $group }}">
                                                    <label class="form-check-label">
                                                        {{ ucfirst(str_replace(['-', '.'], [' ', ' '], $permission->name)) }}
                                                    </label>
                                                </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-cancel me-2" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-submit">Mettre à jour</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DataTable for users
    var usersTable = new DataTable('#usersTable', {
        processing: true,
        serverSide: true,
        ajax: {
            url: "{{ route('users.data') }}",
        },
        columns: [
            { data: 'name', name: 'name' },
            { data: 'email', name: 'email' },
            { data: 'roles', name: 'roles', orderable: false, searchable: false },
            { data: 'created_at', name: 'created_at' },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        language: {
            search: ' ',
            sLengthMenu: '_MENU_',
            searchPlaceholder: "Rechercher",
            info: "_START_ - _END_ sur _TOTAL_ éléments",
            paginate: {
                next: ' <i class="fa fa-angle-right"></i>',
                previous: '<i class="fa fa-angle-left"></i> '
            }
        }
    });

    // Search functionality
    $('#searchInput').on('keyup', function() {
        usersTable.search(this.value).draw();
    });

    // Group checkbox functionality for add role modal
    $('.group-checkbox').on('change', function() {
        var group = $(this).data('group');
        var isChecked = $(this).is(':checked');
        $('input[data-group="' + group + '"].permission-checkbox').prop('checked', isChecked);
    });

    // Group checkbox functionality for edit role modal
    $('.group-checkbox-edit').on('change', function() {
        var group = $(this).data('group');
        var isChecked = $(this).is(':checked');
        $('input[data-group="' + group + '"].permission-checkbox-edit').prop('checked', isChecked);
    });

    // Add User Form
    $('#addUserForm').on('submit', function(e) {
        e.preventDefault();

        // Disable submit button to prevent double submission
        var submitBtn = $(this).find('button[type="submit"]');
        submitBtn.prop('disabled', true).text('Création...');

        $.ajax({
            url: "{{ route('users.store') }}",
            method: 'POST',
            data: $(this).serialize(),
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $('#addUserModal').modal('hide');
                usersTable.ajax.reload();
                showAlert('success', response.message);
                $('#addUserForm')[0].reset();
            },
            error: function(xhr) {
                console.error('Error details:', xhr.responseJSON);
                var errorMessage = 'Erreur lors de la création de l\'utilisateur';

                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    var errors = xhr.responseJSON.errors;
                    errorMessage = Object.values(errors).flat().join('<br>');
                } else if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }

                showAlert('error', errorMessage);
            },
            complete: function() {
                // Re-enable submit button
                submitBtn.prop('disabled', false).text('Ajouter');
            }
        });
    });

    // Edit User Form
    $('#editUserForm').on('submit', function(e) {
        e.preventDefault();
        var userId = $('#editUserId').val();
        $.ajax({
            url: "/users/" + userId,
            method: 'PUT',
            data: $(this).serialize(),
            success: function(response) {
                $('#editUserModal').modal('hide');
                usersTable.ajax.reload();
                showAlert('success', response.message);
            },
            error: function(xhr) {
                showAlert('error', 'Erreur lors de la mise à jour de l\'utilisateur');
            }
        });
    });

    // Add Role Form
    $('#addRoleForm').on('submit', function(e) {
        e.preventDefault();
        $.ajax({
            url: "{{ route('roles.store') }}",
            method: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                $('#addRoleModal').modal('hide');
                location.reload();
                showAlert('success', response.message);
            },
            error: function(xhr) {
                showAlert('error', 'Erreur lors de la création du rôle');
            }
        });
    });

    // Edit Role Form
    $('#editRoleForm').on('submit', function(e) {
        e.preventDefault();
        var roleId = $('#editRoleId').val();
        $.ajax({
            url: "/roles/" + roleId,
            method: 'PUT',
            data: $(this).serialize(),
            success: function(response) {
                $('#editRoleModal').modal('hide');
                location.reload();
                showAlert('success', response.message);
            },
            error: function(xhr) {
                showAlert('error', 'Erreur lors de la mise à jour du rôle');
            }
        });
    });
});

function refreshTable() {
    $('#usersTable').DataTable().ajax.reload();
}

function editUser(id) {
    $.ajax({
        url: "/users/" + id,
        method: 'GET',
        success: function(user) {
            $('#editUserId').val(user.id);
            $('#editUserName').val(user.name);
            $('#editUserEmail').val(user.email);

            // Clear and set roles
            $('#editUserRoles option').prop('selected', false);
            user.roles.forEach(function(role) {
                $('#editUserRoles option[value="' + role.name + '"]').prop('selected', true);
            });

            $('#editUserModal').modal('show');
        }
    });
}

function deleteUser(id) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')) {
        $.ajax({
            url: "/users/" + id,
            method: 'DELETE',
            data: {
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                $('#usersTable').DataTable().ajax.reload();
                showAlert('success', response.message);
            },
            error: function(xhr) {
                showAlert('error', 'Erreur lors de la suppression de l\'utilisateur');
            }
        });
    }
}

function editRole(id) {
    $.ajax({
        url: "/roles/" + id,
        method: 'GET',
        success: function(role) {
            $('#editRoleId').val(role.id);
            $('#editRoleName').val(role.name);

            // Clear all checkboxes
            $('.permission-checkbox-edit').prop('checked', false);
            $('.group-checkbox-edit').prop('checked', false);

            // Set permissions
            role.permissions.forEach(function(permission) {
                $('input[value="' + permission.name + '"].permission-checkbox-edit').prop('checked', true);
            });

            $('#editRoleModal').modal('show');
        }
    });
}

function deleteRole(id) {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce rôle ?')) {
        $.ajax({
            url: "/roles/" + id,
            method: 'DELETE',
            data: {
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                location.reload();
                showAlert('success', response.message);
            },
            error: function(xhr) {
                showAlert('error', 'Erreur lors de la suppression du rôle');
            }
        });
    }
}

function showAlert(type, message) {
    // You can implement your alert system here
    alert(message);
}
</script>

<x-foot />
