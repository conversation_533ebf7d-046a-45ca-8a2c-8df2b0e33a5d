<?php

namespace App\Http\Controllers;

use App\Models\StatutPositionaire;
use Illuminate\Http\Request;

class StatutPositionaireController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(StatutPositionaire $statutPositionaire)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(StatutPositionaire $statutPositionaire)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, StatutPositionaire $statutPositionaire)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(StatutPositionaire $statutPositionaire)
    {
        //
    }
}
