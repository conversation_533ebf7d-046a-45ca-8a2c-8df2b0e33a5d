<!DOCTYPE html>
<html>
<head>
    <title>Test Retraites</title>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Test des fonctions de retraite</h1>
    
    <div>
        <h2>Paramètres actuels</h2>
        <button id="getSettings">Récupérer paramètres</button>
        <div id="settingsResult"></div>
    </div>
    
    <div>
        <h2>Fonctionnaires éligibles</h2>
        <button id="getEligible">Récupérer éligibles</button>
        <div id="eligibleResult"></div>
    </div>
    
    <div>
        <h2>Créer retraites auto</h2>
        <button id="createAuto">Créer auto</button>
        <div id="autoResult"></div>
    </div>

    <script>
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $('#getSettings').click(function() {
            $.get('/retraites/settings')
                .done(function(data) {
                    $('#settingsResult').html('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                })
                .fail(function(xhr) {
                    $('#settingsResult').html('<div style="color: red;">Erreur: ' + xhr.status + ' - ' + xhr.responseText + '</div>');
                });
        });

        $('#getEligible').click(function() {
            $.get('/retraites/filters/eligible')
                .done(function(data) {
                    $('#eligibleResult').html('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                })
                .fail(function(xhr) {
                    $('#eligibleResult').html('<div style="color: red;">Erreur: ' + xhr.status + ' - ' + xhr.responseText + '</div>');
                });
        });

        $('#createAuto').click(function() {
            $.post('/retraites/auto-create')
                .done(function(data) {
                    $('#autoResult').html('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                })
                .fail(function(xhr) {
                    $('#autoResult').html('<div style="color: red;">Erreur: ' + xhr.status + ' - ' + xhr.responseText + '</div>');
                });
        });
    </script>
</body>
</html>
<?php /**PATH E:\apprh12062025\resources\views/test-retraites.blade.php ENDPATH**/ ?>