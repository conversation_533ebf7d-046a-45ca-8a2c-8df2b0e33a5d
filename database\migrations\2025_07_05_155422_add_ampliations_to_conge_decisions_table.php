<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('conge_decisions', function (Blueprint $table) {
            $table->json('ampliations')->nullable()->after('remarques');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('conge_decisions', function (Blueprint $table) {
            $table->dropColumn('ampliations');
        });
    }
};
