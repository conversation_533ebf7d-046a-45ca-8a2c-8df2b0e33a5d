<?php

namespace App\Http\Controllers;

use App\Models\FichiersFonctionnaire;
use Illuminate\Http\Request;

class FichiersFonctionnaireController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(FichiersFonctionnaire $fichiersFonctionnaire)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(FichiersFonctionnaire $fichiersFonctionnaire)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, FichiersFonctionnaire $fichiersFonctionnaire)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FichiersFonctionnaire $fichiersFonctionnaire)
    {
        //
    }
}
