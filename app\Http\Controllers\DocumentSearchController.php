<?php

namespace App\Http\Controllers;

use App\Models\Fonctionnaire;
use App\Models\Certificate;
use App\Models\Relicat;
use App\Models\TypeConge;
use App\Models\CongeDecision;
use App\Models\HistoriqueMutation;
use App\Models\PositionHistorique;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class DocumentSearchController extends Controller
{
    /**
     * Display the global search page
     */
    public function index()
    {
        return view('documents.search');
    }

    /**
     * Perform global search
     */
    public function search(Request $request)
    {
        $query = $request->input('query');
        $documentType = $request->input('document_type', 'all');
        $results = [];

        if (empty($query)) {
            return response()->json([
                'success' => false,
                'message' => 'Veuillez saisir un terme de recherche'
            ]);
        }

        // Search in different document types based on selection
        switch ($documentType) {
            case 'conge':
                $results = $this->searchCongeDocuments($query);
                break;
            case 'certificate':
                $results = $this->searchCertificates($query);
                break;
            case 'attestation':
                $results = $this->searchAttestations($query);
                break;
            case 'stage':
                $results = $this->searchStages($query);
                break;
            case 'mutation':
                $results = $this->searchMutations($query);
                break;
            case 'position':
                $results = $this->searchPositions($query);
                break;
            case 'all':
            default:
                $results = $this->searchAllDocuments($query);
                break;
        }

        return response()->json([
            'success' => true,
            'results' => $results,
            'total' => count($results)
        ]);
    }

    /**
     * Search in congé documents
     */
    private function searchCongeDocuments($query)
    {
        $results = [];

        // Search in Relicat (old congé requests)
        $relicats = Relicat::with(['fonctionnaire', 'typeConge'])
            ->whereHas('fonctionnaire', function($q) use ($query) {
                $q->where('nom', 'LIKE', "%{$query}%")
                  ->orWhere('prenom', 'LIKE', "%{$query}%")
                  ->orWhere('ppr', 'LIKE', "%{$query}%");
            })
            ->orWhereHas('typeConge', function($q) use ($query) {
                $q->where('nom', 'LIKE', "%{$query}%");
            })
            ->get();

        foreach ($relicats as $relicat) {
            $results[] = [
                'type' => 'Congé',
                'title' => "Décision de congé - {$relicat->fonctionnaire->nom} {$relicat->fonctionnaire->prenom}",
                'description' => "Type: {$relicat->typeConge->nom} | Jours disponibles: {$relicat->nbr_jours_disponibles}",
                'url' => route('fonctionnaire.conge-decision', $relicat->id),
                'date' => $relicat->created_at->format('d/m/Y'),
                'fonctionnaire' => $relicat->fonctionnaire->nom . ' ' . $relicat->fonctionnaire->prenom,
                'matricule' => $relicat->fonctionnaire->ppr,
                'fonctionnaire_id' => $relicat->fonctionnaire->id,
                'requires_form' => true,
                'form_type' => 'conge_decision'
            ];
        }

        // Search in CongeDecision (new congé decisions)
        $congeDecisions = CongeDecision::with(['fonctionnaire', 'typeConge'])
            ->whereHas('fonctionnaire', function($q) use ($query) {
                $q->where('nom', 'LIKE', "%{$query}%")
                  ->orWhere('prenom', 'LIKE', "%{$query}%")
                  ->orWhere('ppr', 'LIKE', "%{$query}%");
            })
            ->orWhereHas('typeConge', function($q) use ($query) {
                $q->where('nom', 'LIKE', "%{$query}%");
            })
            ->get();

        foreach ($congeDecisions as $decision) {
            $results[] = [
                'type' => 'Congé',
                'title' => "Décision de congé - {$decision->fonctionnaire->nom} {$decision->fonctionnaire->prenom}",
                'description' => "Type: {$decision->typeConge->nom} | Nombre de jours: {$decision->nombre_jours} | Statut: {$decision->statut}",
                'url' => route('batch-print.print-single-decision-with-ampliations', $decision->id),
                'date' => $decision->date_decision->format('d/m/Y'),
                'fonctionnaire' => $decision->fonctionnaire->nom . ' ' . $decision->fonctionnaire->prenom,
                'matricule' => $decision->fonctionnaire->ppr,
                'fonctionnaire_id' => $decision->fonctionnaire->id,
                'requires_form' => true,
                'form_type' => 'conge_decision'
            ];
        }

        return $results;
    }

    /**
     * Search in certificates
     */
    private function searchCertificates($query)
    {
        $results = [];

        $certificates = Certificate::with(['fonctionnaire', 'type_certificate'])
            ->whereHas('fonctionnaire', function($q) use ($query) {
                $q->where('nom', 'LIKE', "%{$query}%")
                  ->orWhere('prenom', 'LIKE', "%{$query}%")
                  ->orWhere('ppr', 'LIKE', "%{$query}%");
            })
            ->orWhereHas('type_certificate', function($q) use ($query) {
                $q->where('nom', 'LIKE', "%{$query}%");
            })
            ->get();

        foreach ($certificates as $certificate) {
            $results[] = [
                'type' => 'Certificat',
                'title' => "Certificat - {$certificate->fonctionnaire->nom} {$certificate->fonctionnaire->prenom}",
                'description' => "Type: {$certificate->type_certificate->nom}",
                'url' => route('certificates.pdf', $certificate->id),
                'date' => $certificate->created_at->format('d/m/Y'),
                'fonctionnaire' => $certificate->fonctionnaire->nom . ' ' . $certificate->fonctionnaire->prenom,
                'matricule' => $certificate->fonctionnaire->ppr,
                'fonctionnaire_id' => $certificate->fonctionnaire->id,
                'requires_form' => false,
                'form_type' => null
            ];
        }

        return $results;
    }

    /**
     * Search in attestations
     */
    private function searchAttestations($query)
    {
        $results = [];

        $fonctionnaires = Fonctionnaire::where('nom', 'LIKE', "%{$query}%")
            ->orWhere('prenom', 'LIKE', "%{$query}%")
            ->orWhere('ppr', 'LIKE', "%{$query}%")
            ->get();

        foreach ($fonctionnaires as $fonctionnaire) {
            $results[] = [
                'type' => 'Attestation',
                'title' => "Attestation de travail - {$fonctionnaire->nom} {$fonctionnaire->prenom}",
                'description' => "Attestation de travail pour le fonctionnaire",
                'url' => route('fonctionnaire.attestation', $fonctionnaire->id),
                'date' => now()->format('d/m/Y'),
                'fonctionnaire' => $fonctionnaire->nom . ' ' . $fonctionnaire->prenom,
                'matricule' => $fonctionnaire->ppr,
                'fonctionnaire_id' => $fonctionnaire->id,
                'requires_form' => false,
                'form_type' => null
            ];
        }

        return $results;
    }

    /**
     * Search in stages
     */
    private function searchStages($query)
    {
        $results = [];

        $stages = \App\Models\Stage::where('nom_prenom', 'LIKE', "%{$query}%")
            ->orWhere('cin', 'LIKE', "%{$query}%")
            ->with(['ecole', 'option', 'service', 'formationSanitaire', 'encadrant'])
            ->get();

        foreach ($stages as $stage) {
            $results[] = [
                'type' => 'Stage',
                'title' => "Attestation de stage - {$stage->nom_prenom}",
                'description' => "Attestation de stage pour {$stage->nom_prenom} - {$stage->ecole->nom}",
                'url' => route('stages.attestation', $stage->id),
                'date' => $stage->date_debut->format('d/m/Y'),
                'fonctionnaire' => $stage->nom_prenom,
                'matricule' => $stage->cin,
                'requires_form' => false,
                'form_type' => null
            ];
        }

        return $results;
    }

    /**
     * Search in mutations
     */
    private function searchMutations($query)
    {
        $results = [];

        $mutations = HistoriqueMutation::with('fonctionnaire')
            ->whereHas('fonctionnaire', function($q) use ($query) {
                $q->where('nom', 'LIKE', "%{$query}%")
                  ->orWhere('prenom', 'LIKE', "%{$query}%")
                  ->orWhere('ppr', 'LIKE', "%{$query}%");
            })
            ->where('fichiers_notes', '!=', null)
            ->get();

        foreach ($mutations as $mutation) {
            if ($mutation->fichiers_notes && Storage::exists('public/' . $mutation->fichiers_notes)) {
                $results[] = [
                    'type' => 'Mutation',
                    'title' => "Note de mutation - {$mutation->fonctionnaire->nom} {$mutation->fonctionnaire->prenom}",
                    'description' => "Mutation du {$mutation->date_prise_en_service}",
                    'url' => Storage::url($mutation->fichiers_notes),
                    'date' => $mutation->created_at->format('d/m/Y'),
                    'fonctionnaire' => $mutation->fonctionnaire->nom . ' ' . $mutation->fonctionnaire->prenom,
                    'matricule' => $mutation->fonctionnaire->ppr,
                    'requires_form' => false,
                    'form_type' => null
                ];
            }
        }

        return $results;
    }

    /**
     * Search in positions
     */
    private function searchPositions($query)
    {
        $results = [];

        $positions = PositionHistorique::with('fonctionnaire')
            ->whereHas('fonctionnaire', function($q) use ($query) {
                $q->where('nom', 'LIKE', "%{$query}%")
                  ->orWhere('prenom', 'LIKE', "%{$query}%")
                  ->orWhere('ppr', 'LIKE', "%{$query}%");
            })
            ->where('fichiers_notes', '!=', null)
            ->get();

        foreach ($positions as $position) {
            if ($position->fichiers_notes && Storage::exists('public/' . $position->fichiers_notes)) {
                $results[] = [
                    'type' => 'Position',
                    'title' => "Note de position - {$position->fonctionnaire->nom} {$position->fonctionnaire->prenom}",
                    'description' => "Position du {$position->date_prise_en_service}",
                    'url' => Storage::url($position->fichiers_notes),
                    'date' => $position->created_at->format('d/m/Y'),
                    'fonctionnaire' => $position->fonctionnaire->nom . ' ' . $position->fonctionnaire->prenom,
                    'matricule' => $position->fonctionnaire->ppr,
                    'requires_form' => false,
                    'form_type' => null
                ];
            }
        }

        return $results;
    }

    /**
     * Search in all document types
     */
    private function searchAllDocuments($query)
    {
        $results = [];

        $results = array_merge($results, $this->searchCongeDocuments($query));
        $results = array_merge($results, $this->searchCertificates($query));
        $results = array_merge($results, $this->searchAttestations($query));
        $results = array_merge($results, $this->searchStages($query));
        $results = array_merge($results, $this->searchMutations($query));
        $results = array_merge($results, $this->searchPositions($query));

        // Sort by date (newest first)
        usort($results, function($a, $b) {
            return strtotime($b['date']) - strtotime($a['date']);
        });

        return $results;
    }

    /**
     * Show export conges form
     */
    public function showExportCongesForm()
    {
        $typeConges = TypeConge::all();
        return view('documents.export-conges-form', compact('typeConges'));
    }

    /**
     * Print search results
     */
    public function printResults(Request $request)
    {
        $query = $request->input('query');
        $documentType = $request->input('document_type');
        $results = json_decode($request->input('results'), true);

        if (empty($results)) {
            return response()->json(['error' => 'Aucun résultat à imprimer'], 400);
        }

        // Generate PDF with search results
        return $this->generateSearchResultsPDF($query, $documentType, $results);
    }

    /**
     * Generate PDF for search results
     */
    private function generateSearchResultsPDF($query, $documentType, $results)
    {
        $pdf = new \TCPDF();

        // Set document information
        $pdf->SetCreator('Application GRH-DMSPS Fès');
        $pdf->SetAuthor('DMSPS Fès');
        $pdf->SetTitle('Résultats de recherche de documents');
        $pdf->SetSubject('Recherche de documents');

        // Set margins
        $pdf->SetMargins(15, 20, 15);
        $pdf->SetHeaderMargin(10);
        $pdf->SetFooterMargin(15);
        $pdf->SetAutoPageBreak(true, 25);

        // Add page
        $pdf->AddPage();

        // Set font
        $pdf->SetFont('dejavusans', '', 12);

        // Header
        $pdf->SetFont('dejavusans', 'B', 16);
        $pdf->Cell(0, 10, 'DÉLÉGATION DU MINISTÈRE DE LA SANTÉ', 0, 1, 'C');
        $pdf->Cell(0, 8, 'PRÉFECTURE DE FÈS', 0, 1, 'C');
        $pdf->Ln(5);

        $pdf->SetFont('dejavusans', 'B', 14);
        $pdf->Cell(0, 8, 'RÉSULTATS DE RECHERCHE DE DOCUMENTS', 0, 1, 'C');
        $pdf->Ln(5);

        // Search criteria
        $pdf->SetFont('dejavusans', 'B', 11);
        $pdf->Cell(0, 6, 'Critères de recherche :', 0, 1, 'L');
        $pdf->SetFont('dejavusans', '', 10);
        $pdf->Cell(40, 6, 'Terme recherché :', 0, 0, 'L');
        $pdf->Cell(0, 6, $query, 0, 1, 'L');
        $pdf->Cell(40, 6, 'Type de document :', 0, 0, 'L');
        $pdf->Cell(0, 6, $this->getDocumentTypeLabel($documentType), 0, 1, 'L');
        $pdf->Cell(40, 6, 'Nombre de résultats :', 0, 0, 'L');
        $pdf->Cell(0, 6, count($results), 0, 1, 'L');
        $pdf->Cell(40, 6, 'Date d\'impression :', 0, 0, 'L');
        $pdf->Cell(0, 6, date('d/m/Y H:i'), 0, 1, 'L');
        $pdf->Ln(8);

        // Results table header
        $pdf->SetFont('dejavusans', 'B', 10);
        $pdf->SetFillColor(230, 230, 230);
        $pdf->Cell(15, 8, 'N°', 1, 0, 'C', true);
        $pdf->Cell(30, 8, 'Type', 1, 0, 'C', true);
        $pdf->Cell(60, 8, 'Document', 1, 0, 'C', true);
        $pdf->Cell(50, 8, 'Fonctionnaire', 1, 0, 'C', true);
        $pdf->Cell(25, 8, 'PPR', 1, 0, 'C', true);
        $pdf->Cell(25, 8, 'Date', 1, 1, 'C', true);

        // Results data
        $pdf->SetFont('dejavusans', '', 9);
        $pdf->SetFillColor(255, 255, 255);

        foreach ($results as $index => $result) {
            // Check if we need a new page
            if ($pdf->GetY() > 250) {
                $pdf->AddPage();
                // Repeat header
                $pdf->SetFont('dejavusans', 'B', 10);
                $pdf->SetFillColor(230, 230, 230);
                $pdf->Cell(15, 8, 'N°', 1, 0, 'C', true);
                $pdf->Cell(30, 8, 'Type', 1, 0, 'C', true);
                $pdf->Cell(60, 8, 'Document', 1, 0, 'C', true);
                $pdf->Cell(50, 8, 'Fonctionnaire', 1, 0, 'C', true);
                $pdf->Cell(25, 8, 'PPR', 1, 0, 'C', true);
                $pdf->Cell(25, 8, 'Date', 1, 1, 'C', true);
                $pdf->SetFont('dejavusans', '', 9);
            }

            $pdf->Cell(15, 6, $index + 1, 1, 0, 'C');
            $pdf->Cell(30, 6, $result['type'], 1, 0, 'L');
            $pdf->Cell(60, 6, substr($result['title'], 0, 40) . (strlen($result['title']) > 40 ? '...' : ''), 1, 0, 'L');
            $pdf->Cell(50, 6, substr($result['fonctionnaire'], 0, 25) . (strlen($result['fonctionnaire']) > 25 ? '...' : ''), 1, 0, 'L');
            $pdf->Cell(25, 6, $result['matricule'], 1, 0, 'C');
            $pdf->Cell(25, 6, $result['date'], 1, 1, 'C');
        }

        // Footer
        $pdf->Ln(10);
        $pdf->SetFont('dejavusans', '', 10);
        $pdf->Cell(0, 6, 'Document généré le ' . date('d/m/Y à H:i'), 0, 1, 'R');

        // Output PDF
        $filename = 'recherche_documents_' . date('Y-m-d_H-i-s') . '.pdf';
        return response($pdf->Output($filename, 'S'))
            ->header('Content-Type', 'application/pdf')
            ->header('Content-Disposition', 'inline; filename="' . $filename . '"');
    }

    /**
     * Get document type label
     */
    private function getDocumentTypeLabel($type)
    {
        $labels = [
            'all' => 'Tous les documents',
            'conge' => 'Congés',
            'certificate' => 'Certificats',
            'attestation' => 'Attestations',
            'mutation' => 'Mutations',
            'position' => 'Positions'
        ];

        return $labels[$type] ?? 'Non défini';
    }

    /**
     * Generate Note de Service PDF
     */
    public function generateNoteService(Request $request)
    {
        $request->validate([
            'fonctionnaire_id' => 'required',
            'ampliation_destination' => 'required|string'
        ]);

        $fonctionnaire = Fonctionnaire::with(['specialiteGrade.grade.cadre', 'nomFormationSanitaire'])
            ->findOrFail($request->fonctionnaire_id);

        $ampliationDestination = $request->ampliation_destination;

        return $this->generateNoteServicePDF($fonctionnaire, $ampliationDestination);
    }

    /**
     * Generate Note de Service PDF
     */
    private function generateNoteServicePDF($fonctionnaire, $ampliationDestination)
    {
        $pdf = new \TCPDF();

        // Set document information
        $pdf->SetCreator('Application GRH-DMSPS Fès');
        $pdf->SetAuthor('DMSPS Fès');
        $pdf->SetTitle('Note de Service');
        $pdf->SetSubject('Note de Service');

        // Set margins
        $pdf->SetMargins(20, 30, 20);
        $pdf->SetHeaderMargin(15);
        $pdf->SetFooterMargin(20);
        $pdf->SetAutoPageBreak(true, 25);

        // Add page
        $pdf->AddPage();

        // Set font
        $pdf->SetFont('dejavusans', '', 12);

        // Header
        $pdf->SetFont('dejavusans', 'B', 16);
        $pdf->Cell(0, 10, 'DÉLÉGATION DU MINISTÈRE DE LA SANTÉ', 0, 1, 'C');
        $pdf->Cell(0, 8, 'PRÉFECTURE DE FÈS', 0, 1, 'C');
        $pdf->Ln(10);

        // Title
        $pdf->SetFont('dejavusans', 'B', 14);
        $pdf->Cell(0, 10, 'Note de service', 0, 1, 'C');
        $pdf->Ln(10);

        // Content
        $pdf->SetFont('dejavusans', '', 12);

        // Fonctionnaire info line
        $cadreText = '';
        if ($fonctionnaire->specialiteGrade && $fonctionnaire->specialiteGrade->grade && $fonctionnaire->specialiteGrade->grade->cadre) {
            $cadreText = $fonctionnaire->specialiteGrade->grade->cadre->nom;
        }

        $specialiteText = '';
        if ($fonctionnaire->specialiteGrade) {
            $specialiteText = $fonctionnaire->specialiteGrade->nom;
        }

        $formationSanitaireText = '';
        if ($fonctionnaire->nomFormationSanitaire) {
            $formationSanitaireText = $fonctionnaire->nomFormationSanitaire->nom;
        }

        // Main content
        $pdf->Cell(20, 8, 'Mr/Mme', 0, 0, 'L');
        $pdf->SetFont('dejavusans', 'B', 12);
        $pdf->Cell(0, 8, $fonctionnaire->nom . ' ' . $fonctionnaire->prenom, 0, 1, 'L');

        $pdf->SetFont('dejavusans', '', 12);
        $pdf->Cell(0, 8, $cadreText . ', PPR : ' . $fonctionnaire->ppr, 0, 1, 'L');
        $pdf->Ln(3);

        $pdf->Cell(0, 8, 'Spécialité : ' . $specialiteText . ' est affecté à ' . $formationSanitaireText . '.', 0, 1, 'L');
        $pdf->Ln(5);

        $pdf->Cell(0, 8, 'Dès réception de la présente note, l\'intéressé doit se présenter à ' . $ampliationDestination, 0, 1, 'L');
        $pdf->Cell(0, 8, 'qui lui donnera toutes les instructions utiles pour prendre son service.', 0, 1, 'L');
        $pdf->Ln(5);

        $pdf->Cell(0, 8, 'Un compte rendu de prise de service me sera adressé aux fins utiles.', 0, 1, 'L');
        $pdf->Ln(15);

        // Signature
        $pdf->Cell(0, 8, 'Le Délégué Préfectoral', 0, 1, 'R');
        $pdf->Ln(20);

        // Ampliations
        $pdf->SetFont('dejavusans', 'B', 12);
        $pdf->Cell(0, 8, 'Ampliation :', 0, 1, 'L');
        $pdf->SetFont('dejavusans', '', 11);
        $pdf->Cell(0, 6, '- ' . $ampliationDestination, 0, 1, 'L');
        $pdf->Cell(0, 6, '- Intéressé', 0, 1, 'L');
        $pdf->Cell(0, 6, '- Archive', 0, 1, 'L');

        // Output PDF
        $filename = 'note_service_' . $fonctionnaire->ppr . '_' . date('Y-m-d_H-i-s') . '.pdf';
        return response($pdf->Output($filename, 'S'))
            ->header('Content-Type', 'application/pdf')
            ->header('Content-Disposition', 'inline; filename="' . $filename . '"');
    }
}
