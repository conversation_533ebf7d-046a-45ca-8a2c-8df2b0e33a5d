<?php
use App\Http\Controllers\TypeCategorieFormationsController;
use App\Http\Controllers\ArrondissementCommuneController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CadreController;
use App\Http\Controllers\CategorieCadreController;
use App\Http\Controllers\CategorieFormationController;
use App\Http\Controllers\CertificateController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\FonctionController;
use App\Http\Controllers\FonctionnaireController;
use App\Http\Controllers\GradeCadreController;
use App\Http\Controllers\GradeController;
use App\Http\Controllers\HistoriqueMutationController;
use App\Http\Controllers\HistoriquePopulationController;
use App\Http\Controllers\HistoriquePositionController;
use App\Http\Controllers\NiveauCategorieFormationController;
use App\Http\Controllers\NomFormationSanitaireController;
use App\Http\Controllers\PositionController;
use App\Http\Controllers\RegionController;
use App\Http\Controllers\RelicatController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\SpecialiteGradeController;
use App\Http\Controllers\StatisticsController;
use App\Http\Controllers\TypeCategorieFormationController;
use App\Http\Controllers\TypeCertificateController;
use App\Http\Controllers\TypeCongeController;
use App\Http\Controllers\TypeMutationController;
use App\Http\Controllers\TypesCongeController;
use App\Http\Controllers\TypeStageController;
use App\Http\Controllers\VilleController;
use App\Http\Controllers\GreveController;
use App\Http\Controllers\DocumentSearchController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ActivityLogController;
use App\Http\Controllers\RetraiteController;
use App\Http\Controllers\BatchPrintController;
use App\Http\Controllers\StageController;
use App\Http\Controllers\EcoleController;
use App\Http\Controllers\OptionController;
use App\Http\Controllers\EncadrantController;
use App\Models\ArrondissementCommune;
use App\Models\SpecialiteGrade;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect('/statistics/detailed');
})->name('home')->middleware('auth');


Route::get('/login', function () {
    return view('login');
})->name('login')->middleware('guest');


//auth routes
Route::post('/login/post', [AuthController::class, 'login'])->name('login.post')->middleware('guest');
Route::get('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

//user routes
Route::put('/user/update', [AuthController::class, 'update'])->name('user.update')->middleware('auth');



//arrondissements-et-communes
Route::get('/arrondissements-et-communes', [ArrondissementCommuneController::class, 'page'])->name('arrondissements.page')->middleware(['auth', 'permission:parametrage.arrondissements-communes']);
Route::get('/arrondissements-et-communes/data', [ArrondissementCommuneController::class, 'data'])->name('arrondissements.data')->middleware(['auth', 'permission:parametrage.arrondissements-communes']);
Route::post('/arrondissements-et-communes/add', [ArrondissementCommuneController::class, 'add'])->name('arrondissements.add')->middleware(['auth', 'permission:parametrage.arrondissements-communes']);
Route::put('/arrondissements-et-communes/update/{id}', [ArrondissementCommuneController::class, 'update'])->name('arrondissements.update')->middleware(['auth', 'permission:parametrage.arrondissements-communes']);
Route::delete('/arrondissements-et-communes/delete/{id}', [ArrondissementCommuneController::class, 'delete'])->name('arrondissements.delete')->middleware(['auth', 'permission:parametrage.arrondissements-communes']);
Route::get('/arrondissements-et-communes/export', [ArrondissementCommuneController::class, 'export'])->name('arrondissements.export')->middleware(['auth', 'permission:parametrage.arrondissements-communes']);
Route::get('/arrondissements-et-communes/pdf', [ArrondissementCommuneController::class, 'pdf'])->name('arrondissements.pdf')->middleware(['auth', 'permission:parametrage.arrondissements-communes']);



//Région
Route::get('/regions', [RegionController::class, 'page'])->name('regions.page')->middleware(['auth', 'permission:parametrage.regions']);
Route::get('/regions/data', [RegionController::class, 'data'])->name('regions.data')->middleware(['auth', 'permission:parametrage.regions']);
Route::post('/regions/add', [RegionController::class, 'add'])->name('regions.add')->middleware(['auth', 'permission:parametrage.regions']);
Route::put('/regions/update/{id}', [RegionController::class, 'update'])->name('regions.update')->middleware(['auth', 'permission:parametrage.regions']);
Route::delete('/regions/delete/{id}', [RegionController::class, 'delete'])->name('regions.delete')->middleware(['auth', 'permission:parametrage.regions']);
Route::get('/regions/export', [RegionController::class, 'export'])->name('regions.export')->middleware(['auth', 'permission:parametrage.regions']);
Route::get('/regions/pdf', [RegionController::class, 'pdf'])->name('regions.pdf')->middleware(['auth', 'permission:parametrage.regions']);


//villes :
Route::get('/villes', [VilleController::class, 'page'])->name('villes.page')->middleware(['auth', 'permission:parametrage.villes']);
Route::get('/villes/data', [VilleController::class, 'data'])->name('villes.data')->middleware(['auth', 'permission:parametrage.villes']);
Route::post('/villes/add', [VilleController::class, 'add'])->name('villes.add')->middleware(['auth', 'permission:parametrage.villes']);
Route::put('/villes/update/{id}', [VilleController::class, 'update'])->name('villes.update')->middleware(['auth', 'permission:parametrage.villes']);
Route::delete('/villes/delete/{id}', [VilleController::class, 'delete'])->name('villes.delete')->middleware(['auth', 'permission:parametrage.villes']);
Route::get('/villes/export', [VilleController::class, 'export'])->name('villes.export')->middleware(['auth', 'permission:parametrage.villes']);
Route::get('/villes/pdf', [VilleController::class, 'pdf'])->name('villes.pdf')->middleware(['auth', 'permission:parametrage.villes']);

//Grades :
Route::get('/grades', [GradeController::class, 'page'])->name('grades.page')->middleware(['auth', 'permission:parametrage.grades']);
Route::get('/grades/data', [GradeController::class, 'data'])->name('grades.data')->middleware(['auth', 'permission:parametrage.grades']);
Route::post('/grades/add', [GradeController::class, 'add'])->name('grades.add')->middleware(['auth', 'permission:parametrage.grades']);
Route::put('/grades/update/{id}', [GradeController::class, 'update'])->name('grades.update')->middleware(['auth', 'permission:parametrage.grades']);
Route::delete('/grades/delete/{id}', [GradeController::class, 'delete'])->name('grades.delete')->middleware(['auth', 'permission:parametrage.grades']);
Route::get('/grades/export', [GradeController::class, 'export'])->name('grades.export')->middleware('auth');
Route::get('/grades/pdf', [GradeController::class, 'pdf'])->name('grades.pdf')->middleware('auth');
//Positions
Route::get('/positions', [PositionController::class, 'page'])->name('positions.page')->middleware(['auth', 'permission:parametrage.positions']);
Route::get('/positions/data', [PositionController::class, 'data'])->name('positions.data')->middleware(['auth', 'permission:parametrage.positions']);
Route::post('/positions/add', [PositionController::class, 'add'])->name('positions.add')->middleware(['auth', 'permission:parametrage.positions']);
Route::put('/positions/update/{id}', [PositionController::class, 'update'])->name('positions.update')->middleware(['auth', 'permission:parametrage.positions']);
Route::delete('/positions/delete/{id}', [PositionController::class, 'delete'])->name('positions.delete')->middleware(['auth', 'permission:parametrage.positions']);
Route::get('/positions/export', [PositionController::class, 'export'])->name('positions.export')->middleware(['auth', 'permission:parametrage.positions']);
Route::get('/positions/pdf', [PositionController::class, 'pdf'])->name('positions.pdf')->middleware(['auth', 'permission:parametrage.positions']);
//Services
Route::get('/services', [ServiceController::class, 'page'])->name('services.page')->middleware(['auth', 'permission:parametrage.services']);
Route::get('/services/data', [ServiceController::class, 'data'])->name('services.data')->middleware(['auth', 'permission:parametrage.services']);
Route::post('/services/add', [ServiceController::class, 'add'])->name('services.add')->middleware(['auth', 'permission:parametrage.services']);
Route::put('/services/update/{id}', [ServiceController::class, 'update'])->name('services.update')->middleware(['auth', 'permission:parametrage.services']);
Route::delete('/services/delete/{id}', [ServiceController::class, 'delete'])->name('services.delete')->middleware(['auth', 'permission:parametrage.services']);
Route::get('/services/export', [ServiceController::class, 'export'])->name('services.export')->middleware(['auth', 'permission:parametrage.services']);
Route::get('/services/pdf', [ServiceController::class, 'pdf'])->name('services.pdf')->middleware(['auth', 'permission:parametrage.services']);
//fonctions
Route::get('/fonctions', [FonctionController::class, 'page'])->name('fonctions.page')->middleware(['auth', 'permission:parametrage.fonctions']);
Route::get('/fonctions/data', [FonctionController::class, 'data'])->name('fonctions.data')->middleware(['auth', 'permission:parametrage.fonctions']);
Route::post('/fonctions/add', [FonctionController::class, 'add'])->name('fonctions.add')->middleware(['auth', 'permission:parametrage.fonctions']);
Route::put('/fonctions/update/{id}', [FonctionController::class, 'update'])->name('fonctions.update')->middleware(['auth', 'permission:parametrage.fonctions']);
Route::delete('/fonctions/delete/{id}', [FonctionController::class, 'delete'])->name('fonctions.delete')->middleware(['auth', 'permission:parametrage.fonctions']);
Route::get('/fonctions/export', [FonctionController::class, 'export'])->name('fonctions.export')->middleware(['auth', 'permission:parametrage.fonctions']);
Route::get('/fonctions/pdf', [FonctionController::class, 'pdf'])->name('fonctions.pdf')->middleware(['auth', 'permission:parametrage.fonctions']);
//typesconges
Route::get('/conges/types', [TypesCongeController::class, 'page'])->name('conges.types.page')->middleware(['auth', 'permission:parametrage.conges-types']);
Route::get('/conges/types/data', [TypesCongeController::class, 'data'])->name('conges.types.data')->middleware(['auth', 'permission:parametrage.conges-types']);
Route::post('conges/types/add', [TypesCongeController::class, 'add'])->name('conges.types.add')->middleware(['auth', 'permission:parametrage.conges-types']);
Route::put('conges/types/update/{id}', [TypesCongeController::class, 'update'])->name('conges.types.update')->middleware(['auth', 'permission:parametrage.conges-types']);
Route::delete('conges/types/delete/{id}', [TypesCongeController::class, 'delete'])->name('conges.types.delete')->middleware(['auth', 'permission:parametrage.conges-types']);
Route::get('conges/types/export', [TypesCongeController::class, 'export'])->name('conges.types.export')->middleware(['auth', 'permission:parametrage.conges-types']);
Route::get('conges/types/pdf', [TypesCongeController::class, 'pdf'])->name('conges.types.pdf')->middleware(['auth', 'permission:parametrage.conges-types']);
//typescertificats
Route::get('/certificats/types', [TypeCertificateController::class, 'page'])->name('certificats.types.page')->middleware(['auth', 'permission:parametrage.certificats-types']);
Route::get('/certificats/types/data', [TypeCertificateController::class, 'data'])->name('certificats.types.data')->middleware(['auth', 'permission:parametrage.certificats-types']);
Route::post('certificats/types/add', [TypeCertificateController::class, 'add'])->name('certificats.types.add')->middleware(['auth', 'permission:parametrage.certificats-types']);
Route::put('certificats/types/update/{id}', [TypeCertificateController::class, 'update'])->name('certificats.types.update')->middleware(['auth', 'permission:parametrage.certificats-types']);
Route::delete('certificats/types/delete/{id}', [TypeCertificateController::class, 'delete'])->name('certificats.types.delete')->middleware(['auth', 'permission:parametrage.certificats-types']);
Route::get('certificats/types/export', [TypeCertificateController::class, 'export'])->name('certificats.types.export')->middleware(['auth', 'permission:parametrage.certificats-types']);
Route::get('certificats/types/pdf', [TypeCertificateController::class, 'pdf'])->name('certificats.types.pdf')->middleware(['auth', 'permission:parametrage.certificats-types']);
//TypeStage
Route::get('/stages/types', [TypeStageController::class, 'page'])->name('stages.types.page')->middleware(['auth', 'permission:parametrage.stages-types']);
Route::get('/stages/types/data', [TypeStageController::class, 'data'])->name('stages.types.data')->middleware(['auth', 'permission:parametrage.stages-types']);
Route::post('stages/types/add', [TypeStageController::class, 'add'])->name('stages.types.add')->middleware(['auth', 'permission:parametrage.stages-types']);
Route::put('stages/types/update/{id}', [TypeStageController::class, 'update'])->name('stages.types.update')->middleware(['auth', 'permission:parametrage.stages-types']);
Route::delete('stages/types/delete/{id}', [TypeStageController::class, 'delete'])->name('stages.types.delete')->middleware(['auth', 'permission:parametrage.stages-types']);
Route::get('stages/types/export', [TypeStageController::class, 'export'])->name('stages.types.export')->middleware(['auth', 'permission:parametrage.stages-types']);
Route::get('stages/types/pdf', [TypeStageController::class, 'pdf'])->name('stages.types.pdf')->middleware(['auth', 'permission:parametrage.stages-types']);



//categoriecadre
Route::get('/categories-cadres', [CategorieCadreController::class, 'page'])->name('categories-cadres.page')->middleware(['auth', 'permission:cadres.categories-cadres']);
Route::get('/categories-cadres/data', [CategorieCadreController::class, 'data'])->name('categories-cadres.data')->middleware(['auth', 'permission:cadres.categories-cadres']);
Route::post('categories-cadres/add', [CategorieCadreController::class, 'add'])->name('categories-cadres.add')->middleware(['auth', 'permission:cadres.categories-cadres']);
Route::put('categories-cadres/update/{id}', [CategorieCadreController::class, 'update'])->name('categories-cadres.update')->middleware(['auth', 'permission:cadres.categories-cadres']);
Route::delete('categories-cadres/delete/{id}', [CategorieCadreController::class, 'delete'])->name('categories-cadres.delete')->middleware(['auth', 'permission:cadres.categories-cadres']);
Route::get('categories-cadres/export', [CategorieCadreController::class, 'export'])->name('categories-cadres.export')->middleware(['auth', 'permission:cadres.categories-cadres']);
Route::get('categories-cadres/pdf', [CategorieCadreController::class, 'pdf'])->name('categories-cadres.pdf')->middleware(['auth', 'permission:cadres.categories-cadres']);


//cadre
Route::get('/cadres', [CadreController::class, 'page'])->name('cadres.page')->middleware('auth');
Route::get('/cadres/data', [CadreController::class, 'data'])->name('cadres.data')->middleware('auth');
Route::post('cadres/add', [CadreController::class, 'add'])->name('cadres.add')->middleware('auth');
Route::put('cadres/update/{id}', [CadreController::class, 'update'])->name('cadres.update')->middleware('auth');
Route::delete('cadres/delete/{id}', [CadreController::class, 'delete'])->name('cadres.delete')->middleware('auth');
Route::get('cadres/export', [CadreController::class, 'export'])->name('cadres.export')->middleware('auth');
Route::get('cadres/pdf', [CadreController::class, 'pdf'])->name('cadres.pdf')->middleware('auth');



//grades cadres
Route::get('/grades-cadres', [GradeCadreController::class, 'page'])->name('grades-cadres.page')->middleware('auth');
Route::get('/grades-cadres/data', [GradeCadreController::class, 'data'])->name('grades-cadres.data')->middleware('auth');
Route::post('grades-cadres/add', [GradeCadreController::class, 'add'])->name('grades-cadres.add')->middleware('auth');
Route::put('grades-cadres/update/{id}', [GradeCadreController::class, 'update'])->name('grades-cadres.update')->middleware('auth');
Route::delete('grades-cadres/delete/{id}', [GradeCadreController::class, 'delete'])->name('grades-cadres.delete')->middleware('auth');
Route::get('grades-cadres/export', [GradeCadreController::class, 'export'])->name('grades-cadres.export')->middleware('auth');
Route::get('grades-cadres/pdf', [GradeCadreController::class, 'pdf'])->name('grades-cadres.pdf')->middleware('auth');



//specialite grade
Route::get('/specialites-cadres', [SpecialiteGradeController::class, 'page'])->name('specialite-grade.page')->middleware('auth');
Route::get('/specialites-cadres/data', [SpecialiteGradeController::class, 'data'])->name('specialite-grade.data')->middleware('auth');
Route::post('specialites-cadres/add', [SpecialiteGradeController::class, 'add'])->name('specialite-grade.add')->middleware('auth');
Route::put('specialites-cadres/update/{id}', [SpecialiteGradeController::class, 'update'])->name('specialite-grade.update')->middleware('auth');
Route::delete('specialites-cadres/delete/{id}', [SpecialiteGradeController::class, 'delete'])->name('specialite-grade.delete')->middleware('auth');
Route::get('specialites-cadres/export', [SpecialiteGradeController::class, 'export'])->name('specialite-grade.export')->middleware('auth');
Route::get('specialites-cadres/pdf', [SpecialiteGradeController::class, 'pdf'])->name('specialite-grade.pdf')->middleware('auth');



//categorie formation
Route::get('/categories-formation', [CategorieFormationController::class, 'page'])->name('categories-formation.page')->middleware('auth');
Route::get('/categories-formation/data', [CategorieFormationController::class, 'data'])->name('categories-formation.data')->middleware('auth');
Route::post('categories-formation/add', [CategorieFormationController::class, 'add'])->name('categories-formation.add')->middleware('auth');
Route::put('categories-formation/update/{id}', [CategorieFormationController::class, 'update'])->name('categories-formation.update')->middleware('auth');
Route::delete('categories-formation/delete/{id}', [CategorieFormationController::class, 'delete'])->name('categories-formation.delete')->middleware('auth');
Route::get('categories-formation/export', [CategorieFormationController::class, 'export'])->name('categories-formation.export')->middleware('auth');
Route::get('categories-formation/pdf', [CategorieFormationController::class, 'pdf'])->name('categories-formation.pdf')->middleware('auth');

//type etablissement
Route::get('/type-categories-formation', [TypeCategorieFormationController::class, 'page'])->name('type-categories-formation.page')->middleware('auth');
Route::get('/type-categories-formation/data', [TypeCategorieFormationController::class, 'data'])->name('type-categories-formation.data')->middleware('auth');
Route::post('type-categories-formation/add', [TypeCategorieFormationController::class, 'add'])->name('type-categories-formation.add')->middleware('auth');
Route::put('type-categories-formation/update/{id}', [TypeCategorieFormationController::class, 'update'])->name('type-categories-formation.update')->middleware('auth');
Route::delete('type-categories-formation/delete/{id}', [TypeCategorieFormationController::class, 'delete'])->name('type-categories-formation.delete')->middleware('auth');
Route::get('type-categories-formation/export', [TypeCategorieFormationController::class, 'export'])->name('type-categories-formation.export')->middleware('auth');
Route::get('type-categories-formation/pdf', [TypeCategorieFormationController::class, 'pdf'])->name('type-categories-formation.pdf')->middleware('auth');

//niveau categories formation

Route::get('/niveau-categories-formation', [NiveauCategorieFormationController::class, 'page'])->name('niveau-categories-formation.page')->middleware('auth');
Route::get('/niveau-categories-formation/data', [NiveauCategorieFormationController::class, 'data'])->name('niveau-categories-formation.data')->middleware('auth');
Route::post('niveau-categories-formation/add', [NiveauCategorieFormationController::class, 'add'])->name('niveau-categories-formation.add')->middleware('auth');
Route::put('niveau-categories-formation/update/{id}', [NiveauCategorieFormationController::class, 'update'])->name('niveau-categories-formation.update')->middleware('auth');
Route::delete('niveau-categories-formation/delete/{id}', [NiveauCategorieFormationController::class, 'delete'])->name('niveau-categories-formation.delete')->middleware('auth');
Route::get('niveau-categories-formation/export', [NiveauCategorieFormationController::class, 'export'])->name('niveau-categories-formation.export')->middleware('auth');
Route::get('niveau-categories-formation/pdf', [NiveauCategorieFormationController::class, 'pdf'])->name('niveau-categories-formation.pdf')->middleware('auth');



//nom categories formation
Route::get('/nom-formation-sanitaire', [NomFormationSanitaireController::class, 'page'])->name('nom-formation-sanitaire.page')->middleware('auth');
Route::get('/nom-formation-sanitaire/data', [NomFormationSanitaireController::class, 'data'])->name('nom-formation-sanitaire.data')->middleware('auth');
Route::post('nom-formation-sanitaire/add', [NomFormationSanitaireController::class, 'add'])->name('nom-formation-sanitaire.add')->middleware('auth');
Route::put('nom-formation-sanitaire/update/{id}', [NomFormationSanitaireController::class, 'update'])->name('nom-formation-sanitaire.update')->middleware('auth');
Route::delete('nom-formation-sanitaire/delete/{id}', [NomFormationSanitaireController::class, 'delete'])->name('nom-formation-sanitaire.delete')->middleware('auth');
Route::get('nom-formation-sanitaire/export', [NomFormationSanitaireController::class, 'export'])->name('nom-formation-sanitaire.export')->middleware('auth');
Route::get('nom-formation-sanitaire/pdf', [NomFormationSanitaireController::class, 'pdf'])->name('nom-formation-sanitaire.pdf')->middleware('auth');


//population
Route::post('population/add', [HistoriquePopulationController::class, 'add'])->name('population.add')->middleware('auth');
Route::put('population/update/{id}', [HistoriquePopulationController::class, 'update'])->name('population.update')->middleware('auth');
Route::delete('population/delete/{id}', [HistoriquePopulationController::class, 'delete'])->name('population.delete')->middleware('auth');



//type mutation
Route::get('/type-de-mutation', [TypeMutationController::class, 'page'])->name('type-de-mutation.page')->middleware('auth');
Route::get('/type-de-mutation/data', [TypeMutationController::class, 'data'])->name('type-de-mutation.data')->middleware('auth');
Route::post('type-de-mutation/add', [TypeMutationController::class, 'add'])->name('type-de-mutation.add')->middleware('auth');
Route::put('type-de-mutation/update/{id}', [TypeMutationController::class, 'update'])->name('type-de-mutation.update')->middleware('auth');
Route::delete('type-de-mutation/delete/{id}', [TypeMutationController::class, 'delete'])->name('type-de-mutation.delete')->middleware('auth');
Route::get('type-de-mutation/export', [TypeMutationController::class, 'export'])->name('type-de-mutation.export')->middleware('auth');
Route::get('type-de-mutation/pdf', [TypeMutationController::class, 'pdf'])->name('type-de-mutation.pdf')->middleware('auth');



//fonctionnaire
Route::get('/fonctionnaires', [FonctionnaireController::class, 'page'])->name('fonctionnaire.page')->middleware(['auth', 'permission:fonctionnaires.view']);
Route::get('/fonctionnaire/data', [FonctionnaireController::class, 'data'])->name('fonctionnaire.data')->middleware(['auth', 'permission:fonctionnaires.view']);
// Route::get('/fonctionnaire/{id}', [FonctionnaireController::class, 'show'])->name('fonctionnaire.show')->middleware('auth');
Route::post('fonctionnaire/add', [FonctionnaireController::class, 'add'])->name('fonctionnaire.add')->middleware(['auth', 'permission:fonctionnaires.create']);
Route::put('fonctionnaire/update/{id}', [FonctionnaireController::class, 'update'])->name('fonctionnaire.update')->middleware(['auth', 'permission:fonctionnaires.edit']);
Route::delete('fonctionnaire/delete/{id}', [FonctionnaireController::class, 'delete'])->name('fonctionnaire.delete')->middleware(['auth', 'permission:fonctionnaires.delete']);
Route::get('fonctionnaire/export', [FonctionnaireController::class, 'export'])->name('fonctionnaire.export')->middleware(['auth', 'permission:fonctionnaires.view']);
Route::get('fonctionnaire/pdf', [FonctionnaireController::class, 'pdf'])->name('fonctionnaire.pdf')->middleware(['auth', 'permission:fonctionnaires.view']);
Route::get('fonctionnaire/{id}/', [FonctionnaireController::class, 'show'])->name('fonctionnaire.show')->middleware(['auth', 'permission:fonctionnaires.view']);
Route::get('fonctionnaire/attestation-travail/{id}/', [FonctionnaireController::class, 'attestation'])->name('fonctionnaire.attestation')->middleware(['auth', 'permission:fonctionnaires.view']);
Route::post('fonctionnaire/export-assignments', [FonctionnaireController::class, 'exportAssignments'])->name('fonctionnaire.export-assignments')->middleware('auth');
Route::post('fonctionnaire/export-assignments-word', [FonctionnaireController::class, 'exportAssignmentsWord'])->name('fonctionnaire.export-assignments-word')->middleware('auth');
Route::post('fonctionnaire/export-conges', [FonctionnaireController::class, 'exportConges'])->name('fonctionnaire.export-conges')->middleware('auth');
Route::post('fonctionnaire/conge-decision/{relicatId}', [FonctionnaireController::class, 'congeDecision'])->name('fonctionnaire.conge-decision')->middleware('auth');




//historique mutation
Route::post('historique-mutation/add', [HistoriqueMutationController::class, 'add'])->name('historique-mutation.add')->middleware('auth');
Route::put('historique-mutation/update/{id}', [HistoriqueMutationController::class, 'update'])->name('historique-mutation.update')->middleware('auth');
Route::delete('historique-mutation/delete/{id}', [HistoriqueMutationController::class, 'delete'])->name('historique-mutation.delete')->middleware('auth');


//historique position
Route::post('historique-position/add', [HistoriquePositionController::class, 'add'])->name('historique-position.add')->middleware('auth');
Route::put('historique-position/update/{id}', [HistoriquePositionController::class, 'update'])->name('historique-position.update')->middleware('auth');
Route::delete('historique-position/delete/{id}', [HistoriquePositionController::class, 'delete'])->name('historique-position.delete')->middleware('auth');


//Reliquat
Route::post('reliquat/add', [RelicatController::class, 'add'])->name('reliquat.add')->middleware('auth');
Route::delete('reliquat-position/delete/{id}', [RelicatController::class, 'delete'])->name('reliquat.delete')->middleware('auth');

//Batch Print - Impression en lot
Route::get('/impression-lot', [BatchPrintController::class, 'index'])->name('batch-print.index')->middleware(['auth', 'permission:batch-print.view']);
Route::post('/impression-lot/attestations', [BatchPrintController::class, 'batchAttestations'])->name('batch-print.attestations')->middleware(['auth', 'permission:batch-print.attestations']);
Route::get('/impression-lot/conge-decisions', [BatchPrintController::class, 'congeDecisions'])->name('batch-print.conge-decisions')->middleware(['auth', 'permission:batch-print.conge-decisions']);
Route::post('/impression-lot/conge-decision', [BatchPrintController::class, 'storeCongeDecision'])->name('batch-print.store-conge-decision')->middleware(['auth', 'permission:batch-print.conge-decisions']);
Route::get('/impression-lot/export-conge-decisions', [BatchPrintController::class, 'exportCongeDecisions'])->name('batch-print.export-conge-decisions')->middleware(['auth', 'permission:batch-print.conge-decisions']);
Route::get('/impression-lot/print-decision/{id}', [BatchPrintController::class, 'printSingleDecision'])->name('batch-print.print-single-decision')->middleware(['auth']);
// Route temporaire pour test PDF sans permissions
Route::get('/test-pdf-decision/{id}', [BatchPrintController::class, 'printSingleDecision'])->name('test.pdf.decision');
Route::post('/impression-lot/print-decisions', [BatchPrintController::class, 'printDecisions'])->name('batch-print.print-decisions')->middleware(['auth', 'permission:batch-print.conge-decisions']);
Route::delete('/impression-lot/delete-decision/{id}', [BatchPrintController::class, 'deleteDecision'])->name('batch-print.delete-decision')->middleware(['auth', 'permission:batch-print.conge-decisions']);

//Certificate
Route::post('certificate/add', [CertificateController::class, 'add'])->name('certificate.add')->middleware('auth');
Route::delete('certificate/delete/{id}', [CertificateController::class, 'delete'])->name('certificate.delete')->middleware('auth');

// Add this route for certificate PDF download
Route::get('/certificates/pdf/{id}', [CertificateController::class, 'pdf'])->name('certificates.pdf')->middleware('auth');

//Tableau de bord
Route::get('/tableau-de-bord', [DashboardController::class, 'page'])->name('tableau-de-bord.page')->middleware(['auth', 'permission:administration.tableau-de-bord']);

// Statistics routes
Route::get('/statistics/detailed', [StatisticsController::class, 'detailedStatistics'])->name('statistics.detailed')->middleware(['auth']);
Route::get('/statistics/hr', [StatisticsController::class, 'hrStatistics'])->name('statistics.hr')->middleware(['auth', 'permission:administration.statistics-hr']);
Route::get('/statistics/hr/test', [StatisticsController::class, 'testHrStatistics'])->name('statistics.hr.test');

// AI Analysis routes
Route::get('/statistics/ai-analyses', [StatisticsController::class, 'aiAnalyses'])->name('statistics.ai-analyses')->middleware(['auth', 'permission:statistics.ai-analyses']);
Route::post('/statistics/ai-analyses/generate', [StatisticsController::class, 'generateAIAnalysis'])->name('statistics.ai-analyses.generate')->middleware(['auth', 'permission:statistics.ai-analyses']);
Route::post('/statistics/ai-analyses/export-pdf', [StatisticsController::class, 'exportAnalysisPDF'])->name('statistics.ai-analyses.export-pdf')->middleware(['auth', 'permission:statistics.ai-analyses']);

Route::resource('greves', GreveController::class)->middleware(['auth', 'permission:greves.view']);

// Document Search routes
Route::get('/documents/search', [DocumentSearchController::class, 'index'])->name('documents.search')->middleware('auth');
Route::post('/documents/search', [DocumentSearchController::class, 'search'])->name('documents.search.post')->middleware('auth');
Route::get('/documents/export-conges-form', [DocumentSearchController::class, 'showExportCongesForm'])->name('documents.export-conges-form')->middleware('auth');

// User Management routes
Route::get('/utilisateurs', [UserController::class, 'page'])->name('users.page')->middleware(['auth', 'permission:users.view']);
Route::get('/users/data', [UserController::class, 'data'])->name('users.data')->middleware(['auth', 'permission:users.view']);
Route::post('/users', [UserController::class, 'store'])->name('users.store')->middleware(['auth', 'permission:users.create']);
Route::get('/users/{id}', [UserController::class, 'show'])->name('users.show')->middleware(['auth', 'permission:users.view']);
Route::put('/users/{id}', [UserController::class, 'update'])->name('users.update')->middleware(['auth', 'permission:users.edit']);
Route::delete('/users/{id}', [UserController::class, 'destroy'])->name('users.destroy')->middleware(['auth', 'permission:users.delete']);

// Role Management routes
Route::post('/roles', [UserController::class, 'storeRole'])->name('roles.store')->middleware(['auth', 'permission:roles.create']);
Route::get('/roles/{id}', [UserController::class, 'showRole'])->name('roles.show')->middleware(['auth', 'permission:roles.view']);
Route::put('/roles/{id}', [UserController::class, 'updateRole'])->name('roles.update')->middleware(['auth', 'permission:roles.edit']);
Route::delete('/roles/{id}', [UserController::class, 'destroyRole'])->name('roles.destroy')->middleware(['auth', 'permission:roles.delete']);

// Activity Log routes
Route::get('/journal-activite', [ActivityLogController::class, 'index'])->name('activity-logs.index')->middleware(['auth', 'permission:activity-logs.view']);
Route::get('/activity-logs/data', [ActivityLogController::class, 'data'])->name('activity-logs.data')->middleware(['auth', 'permission:activity-logs.view']);
Route::get('/activity-logs/{id}', [ActivityLogController::class, 'show'])->name('activity-logs.show')->middleware(['auth', 'permission:activity-logs.view']);
Route::get('/activity-logs/filters/log-names', [ActivityLogController::class, 'getLogNames'])->name('activity-logs.log-names')->middleware('auth');
Route::get('/activity-logs/filters/events', [ActivityLogController::class, 'getEvents'])->name('activity-logs.events')->middleware('auth');
Route::get('/activity-logs/filters/users', [ActivityLogController::class, 'getUsers'])->name('activity-logs.users')->middleware('auth');
Route::get('/activity-logs/filters/subject-types', [ActivityLogController::class, 'getSubjectTypes'])->name('activity-logs.subject-types')->middleware('auth');

// Retirement Management routes
Route::get('/retraites', [RetraiteController::class, 'index'])->name('retraites.index')->middleware(['auth', 'permission:retraites.view']);
Route::get('/retraites/data', [RetraiteController::class, 'data'])->name('retraites.data')->middleware(['auth', 'permission:retraites.view']);
Route::post('/retraites', [RetraiteController::class, 'store'])->name('retraites.store')->middleware(['auth', 'permission:retraites.create']);
Route::get('/retraites/{id}', [RetraiteController::class, 'show'])->name('retraites.show')->middleware(['auth', 'permission:retraites.view']);
Route::put('/retraites/{id}', [RetraiteController::class, 'update'])->name('retraites.update')->middleware(['auth', 'permission:retraites.edit']);
Route::delete('/retraites/{id}', [RetraiteController::class, 'destroy'])->name('retraites.destroy')->middleware(['auth', 'permission:retraites.delete']);
Route::post('/retraites/{id}/validate', [RetraiteController::class, 'validate'])->name('retraites.validate')->middleware(['auth', 'permission:retraites.validate']);
Route::get('/retraites/filters/fonctionnaires', [RetraiteController::class, 'getFonctionnaires'])->name('retraites.fonctionnaires')->middleware('auth');
Route::get('/retraites/filters/eligible', [RetraiteController::class, 'getEligibleFonctionnaires'])->name('retraites.eligible')->middleware('auth');
Route::post('/retraites/auto-create', [RetraiteController::class, 'createAutoRetirements'])->name('retraites.auto-create')->middleware('auth');

// Routes pour la gestion des stages
Route::get('/stages', [StageController::class, 'index'])->name('stages.index')->middleware(['auth', 'permission:stages.view']);
Route::get('/stages/data', [StageController::class, 'data'])->name('stages.data')->middleware(['auth', 'permission:stages.view']);
Route::get('/stages/create', [StageController::class, 'create'])->name('stages.create')->middleware(['auth', 'permission:stages.create']);
Route::post('/stages', [StageController::class, 'store'])->name('stages.store')->middleware(['auth', 'permission:stages.create']);
Route::get('/stages/{id}', [StageController::class, 'show'])->name('stages.show')->middleware(['auth', 'permission:stages.view']);
Route::get('/stages/{id}/edit', [StageController::class, 'edit'])->name('stages.edit')->middleware(['auth', 'permission:stages.edit']);
Route::put('/stages/{id}', [StageController::class, 'update'])->name('stages.update')->middleware(['auth', 'permission:stages.edit']);
Route::delete('/stages/{id}', [StageController::class, 'destroy'])->name('stages.destroy')->middleware(['auth', 'permission:stages.delete']);
Route::get('/stages/export/excel', [StageController::class, 'export'])->name('stages.export')->middleware(['auth', 'permission:stages.export']);
Route::get('/stages/export/pdf', [StageController::class, 'pdf'])->name('stages.pdf')->middleware(['auth', 'permission:stages.export']);
Route::get('/stages/attestation/{id}', [StageController::class, 'attestation'])->name('stages.attestation')->middleware(['auth', 'permission:stages.attestation']);

// Routes AJAX pour la gestion des entités liées
// Écoles
Route::get('/api/ecoles', [EcoleController::class, 'index'])->name('api.ecoles.index')->middleware(['auth', 'permission:stages.manage-ecoles']);
Route::get('/api/ecoles/data', [EcoleController::class, 'data'])->name('api.ecoles.data')->middleware(['auth', 'permission:stages.manage-ecoles']);
Route::post('/api/ecoles', [EcoleController::class, 'store'])->name('api.ecoles.store')->middleware(['auth', 'permission:stages.manage-ecoles']);
Route::get('/api/ecoles/{id}', [EcoleController::class, 'show'])->name('api.ecoles.show')->middleware(['auth', 'permission:stages.manage-ecoles']);
Route::put('/api/ecoles/{id}', [EcoleController::class, 'update'])->name('api.ecoles.update')->middleware(['auth', 'permission:stages.manage-ecoles']);
Route::delete('/api/ecoles/{id}', [EcoleController::class, 'destroy'])->name('api.ecoles.destroy')->middleware(['auth', 'permission:stages.manage-ecoles']);

// Options
Route::get('/api/options', [OptionController::class, 'index'])->name('api.options.index')->middleware(['auth', 'permission:stages.manage-options']);
Route::get('/api/options/data', [OptionController::class, 'data'])->name('api.options.data')->middleware(['auth', 'permission:stages.manage-options']);
Route::post('/api/options', [OptionController::class, 'store'])->name('api.options.store')->middleware(['auth', 'permission:stages.manage-options']);
Route::get('/api/options/{id}', [OptionController::class, 'show'])->name('api.options.show')->middleware(['auth', 'permission:stages.manage-options']);
Route::put('/api/options/{id}', [OptionController::class, 'update'])->name('api.options.update')->middleware(['auth', 'permission:stages.manage-options']);
Route::delete('/api/options/{id}', [OptionController::class, 'destroy'])->name('api.options.destroy')->middleware(['auth', 'permission:stages.manage-options']);

// Encadrants
Route::get('/api/encadrants', [EncadrantController::class, 'index'])->name('api.encadrants.index')->middleware(['auth', 'permission:stages.manage-encadrants']);
Route::get('/api/encadrants/data', [EncadrantController::class, 'data'])->name('api.encadrants.data')->middleware(['auth', 'permission:stages.manage-encadrants']);
Route::post('/api/encadrants', [EncadrantController::class, 'store'])->name('api.encadrants.store')->middleware(['auth', 'permission:stages.manage-encadrants']);
Route::get('/api/encadrants/{id}', [EncadrantController::class, 'show'])->name('api.encadrants.show')->middleware(['auth', 'permission:stages.manage-encadrants']);
Route::put('/api/encadrants/{id}', [EncadrantController::class, 'update'])->name('api.encadrants.update')->middleware(['auth', 'permission:stages.manage-encadrants']);
Route::delete('/api/encadrants/{id}', [EncadrantController::class, 'destroy'])->name('api.encadrants.destroy')->middleware(['auth', 'permission:stages.manage-encadrants']);
