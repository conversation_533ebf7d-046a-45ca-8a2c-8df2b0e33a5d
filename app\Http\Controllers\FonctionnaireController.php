<?php

namespace App\Http\Controllers;

use App\Models\Cadre;
use App\Models\Fonction;
use App\Models\Fonctionnaire;
use App\Models\Grade;
use App\Models\GradeCadre;
use App\Models\NomFormationSanitaire;
use App\Models\Position;
use App\Models\Relicat;
use App\Models\Service;
use App\Models\SpecialiteGrade;
use App\Models\TypeConge;
use App\Models\TypeMutation;
use App\Models\HistoriqueMutation;
use App\Utils\CustomPDF;
use Illuminate\Http\Request;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use Carbon\Carbon;
use Yajra\DataTables\Facades\DataTables;

class FonctionnaireController extends Controller
{

    public function page()
    {
        // 'fonction_id' => 'required',
        // 'nom_formation_sanitaire_id' => 'nullable',
        // 'service_id' => 'required',
        // 'specialite_grade_id' => 'required',
        // 'position_id' => 'required',


        $fonctions  = Fonction::all();
        $nomFormationSanitaires = NomFormationSanitaire::all();
        $services = Service::all();
        $specialite_grades = SpecialiteGrade::all();
        $positions = Position::all();
        $typemutations = TypeMutation::all();
        $cadres = Cadre::all();


        return view('fonctionnaire.page', compact('fonctions', 'nomFormationSanitaires', 'services', 'specialite_grades', 'positions', 'typemutations', 'cadres'));
    }

    public function add(Request $request)
    {
        $request->validate([
            'ppr' => 'required|string|max:255',
            'nom' => 'required|string|max:255',
            'nom_arabe' => 'nullable|string|max:255',
            'prenom' => 'nullable|string|max:255',
            'prenom_arabe' => 'nullable|string|max:255',
            'cin' => 'required|string|max:255',
            'sexe' => 'required|string|max:1',
            'date_naissance' => 'required|date',
            'date_embauche' => 'required|date',
            'email' => 'nullable|email|max:255',
            'telephone' => 'nullable|string|max:255',
            'adresse' => 'nullable|string',
            'fonction_id' => 'required',
            'nom_formation_sanitaire_id' => 'nullable',
            'remarques' => 'nullable|string',
            'service_id' => 'required',
            'specialite_grade_id' => 'required',
            'position_id' => 'required',
            'type_mutation' => 'nullable|string|max:255',
            'date_mutation' => 'nullable|date',
            'date_prise_en_service' => 'nullable|date',
            'date_note' => 'nullable|date',
            'fichier_note' => 'nullable|file|mimes:pdf,doc,docx,png,jpg,jpeg',
            'ref_note' => 'nullable|string|max:255',
        ]);


        if ($request->hasFile('fichier_note')) {
            $fichier_note = $request->file('fichier_note');
            $fichier_note_name = time() . '.' . $fichier_note->getClientOriginalExtension();
            $fichier_note->move(public_path('storage'), $fichier_note_name);
        }

        try {
            Fonctionnaire::create([
                'ppr' => $request->input('ppr') ?? "",
                'nom' => $request->input('nom') ?? "",
                'nom_arabe' => $request->input('nom_arabe') ?? "",
                'prenom' => $request->input('prenom') ?? "",
                'prenom_arabe' => $request->input('prenom_arabe') ?? "",
                'cin' => $request->input('cin') ?? "",
                'sexe' => $request->input('sexe') ?? "",
                'date_naissance' => $request->input('date_naissance') ?? "",
                'date_embauche' => $request->input('date_embauche') ?? "",
                'email' => $request->input('email') ?? "",
                'telephone' => $request->input('telephone') ?? "",
                'adresse' => $request->input('adresse') ?? "",
                'fonction_id' => $request->input('fonction_id') ?? "",
                'nom_formation_sanitaire_id' => $request->input('nom_formation_sanitaire_id') ?? "",
                'remarques' => $request->input('remarques') ?? "",
                'service_id' => $request->input('service_id') ?? "",
                'specialite_grade_id' => $request->input('specialite_grade_id') ?? "",
                'position_id' => $request->input('position_id') ?? "",
                'type_mutation' => $request->input('type_mutation') ?? "",
                'date_mutation' => $request->input('date_mutation') ?? "",
                'date_prise_en_service' => $request->input('date_prise_en_service') ?? "",
                'date_note' => $request->input('date_note') ?? "",
                'fichier_note' => $fichier_note_name ?? "",
                'ref_note' => $request->input('ref_note') ?? "",
            ]);

            return redirect()->back()->with('success', 'Fonctionnaire ajouté avec succès');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Une erreur s\'est produite lors de l\'ajout de Fonctionnaire.');
        }
    }



    public function update(Request $request, $id)
    {
        $request->validate([
            'ppr' => 'required|string|max:255',
            'nom' => 'required|string|max:255',
            'nom_arabe' => 'nullable|string|max:255',
            'prenom' => 'required|string|max:255',
            'prenom_arabe' => 'nullable|string|max:255',
            'cin' => 'required|string|max:255',
            'sexe' => 'required|string|max:1',
            'date_naissance' => 'required|date',
            'date_embauche' => 'required|date',
            'email' => 'nullable|email|max:255',
            'telephone' => 'nullable|string|max:255',
            'adresse' => 'nullable|string',
            'fonction_id' => 'required',
            'nom_formation_sanitaire_id' => 'nullable',
            'remarques' => 'nullable|string',
            'service_id' => 'required',
            'specialite_grade_id' => 'required',
            'position_id' => 'required',
            'type_mutation' => 'nullable|string|max:255',
            'date_mutation' => 'nullable|date',
            'date_prise_en_service' => 'nullable|date',
            'date_note' => 'nullable|date',
            'fichier_note' => 'nullable|file|mimes:pdf,doc,docx,png,jpg,jpeg',
            'ref_note' => 'nullable|string|max:255',

        ]);

        if ($request->hasFile('fichier_note')) {
            $fichier_note = $request->file('fichier_note');
            $fichier_note_name = time() . '.' . $fichier_note->getClientOriginalExtension();
            $fichier_note->move(public_path('storage'), $fichier_note_name);
        }

        try {
            $fonctionnaire = Fonctionnaire::findOrFail($id);
            $fonctionnaire->update($request->only([
                'ppr',
                'nom',
                'nom_arabe',
                'prenom',
                'prenom_arabe',
                'cin',
                'sexe',
                'date_naissance',
                'date_embauche',
                'email',
                'telephone',
                'adresse',
                'fonction_id',
                'nom_formation_sanitaire_id',
                'remarques',
                'service_id',
                'specialite_grade_id',
                'position_id',
                'type_mutation',
                'date_mutation',
                'date_prise_en_service',
                'date_note',
                'ref_note',

            ]));

            $fonctionnaire->fichier_note = $fichier_note_name;
            $fonctionnaire->save();

            return redirect()->back()->with('success', 'Fonctionnaire mis à jour avec succès');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Une erreur s\'est produite lors de la mise à jour du fonctionnaire.');
        }
    }






    public function data(Request $request)
    {
        $query = Fonctionnaire::query();

        // Handle universal search
        if ($universalSearch = $request->input('universal_search')) {
            $query->where(function ($q) use ($universalSearch) {
                // Search in basic fields
                $q->where('nom', 'like', "%$universalSearch%")
                    ->orWhere('nom_arabe', 'like', "%$universalSearch%")
                    ->orWhere('prenom', 'like', "%$universalSearch%")
                    ->orWhere('prenom_arabe', 'like', "%$universalSearch%")
                    ->orWhere('ppr', 'like', "%$universalSearch%")
                    ->orWhere('cin', 'like', "%$universalSearch%")
                    ->orWhere('email', 'like', "%$universalSearch%")
                    ->orWhere('telephone', 'like', "%$universalSearch%")
                    ->orWhere('adresse', 'like', "%$universalSearch%")
                    // Search in related tables
                    ->orWhereHas('nomFormationSanitaire', function ($q) use ($universalSearch) {
                        $q->where('nom', 'like', "%$universalSearch%")
                            ->orWhere('nom_arabe', 'like', "%$universalSearch%");
                    })
                    ->orWhereHas('specialiteGrade', function ($q) use ($universalSearch) {
                        $q->where('nom', 'like', "%$universalSearch%")
                            ->orWhere('nom_arabe', 'like', "%$universalSearch%");
                    })
                    ->orWhereHas('service', function ($q) use ($universalSearch) {
                        $q->where('nom', 'like', "%$universalSearch%")
                            ->orWhere('nom_arabe', 'like', "%$universalSearch%");
                    })
                    ->orWhereHas('fonction', function ($q) use ($universalSearch) {
                        $q->where('nom', 'like', "%$universalSearch%")
                            ->orWhere('nom_arabe', 'like', "%$universalSearch%");
                    })
                    ->orWhereHas('position', function ($q) use ($universalSearch) {
                        $q->where('nom', 'like', "%$universalSearch%")
                            ->orWhere('nom_arabe', 'like', "%$universalSearch%");
                    });
            });
        }

        // Handle default DataTable search (for backward compatibility)
        elseif ($search = $request->input('search.value')) {
            $query->where(function ($q) use ($search) {
                $q->where('nom', 'like', "%$search%")
                    ->orWhere('prenom', 'like', "%$search%")
                    ->orWhere('ppr', 'like', "%$search%");
            });
        }

        //date naissance
        if ($request->has('date_naissance_de') && $request->has('date_naissance_a')) {
            $query->whereBetween('date_naissance', [$request->date_naissance_de, $request->date_naissance_a]);
        }
        //date embauche
        if ($request->has('date_embauche_de') && $request->has('date_embauche_a')) {
            $query->whereBetween('date_embauche', [$request->date_embauche_de, $request->date_embauche_a]);
        }

        //service
        if ($request->has('service')) {
            $query->where('service_id', $request->service);
        }


        //position
        if ($request->has('position')) {
            $query->where('position_id', $request->position);
        }


        //specialite
        if ($request->has('specialite')) {
            $query->where('specialite_grade_id', $request->specialite);
        }

        //cadre
        if ($request->has('cadre')) {
            $cadre = Cadre::find($request->cadre);
            $grade = GradeCadre::where('cadre_id', $cadre->id)->get();
            $specialite_grades = SpecialiteGrade::whereIn('grade_id', $grade->pluck('id'))->pluck('id');
            $query->whereIn('specialite_grade_id', $specialite_grades);
        }




        $query->with([
            'nomFormationSanitaire' => function ($q) {
                $q->select('id', 'nom', 'nom_arabe');
            },
            'specialiteGrade' => function ($q) {
                $q->select('id', 'nom', 'nom_arabe');
            }
        ]);



        return DataTables::of($query)
            ->addColumn('nom_prenom', function ($data) {
                return $data->nom . ' ' . $data->prenom . ' / ' . $data->nom_arabe . ' ' . $data->prenom_arabe;
            })
            ->addColumn('nom_formation_sanitaire', function ($data) {
                if ($data->nomFormationSanitaire) {
                    return $data->nomFormationSanitaire->nom . ' / ' . $data->nomFormationSanitaire->nom_arabe;
                } else {
                    return '-';
                }
            })
            ->addColumn('specialite_grade', function ($data) {


                if ($data->specialiteGrade) {
                    return      $data->specialiteGrade->nom . ' / ' . $data->specialiteGrade->nom_arabe;
                } else {
                    return '-';
                }
            })
            ->addColumn('action', function ($data) {
                return view('fonctionnaire.action', compact('data'))->render();
            })
            ->rawColumns(['action', 'nom_prenom', 'nom_formation_sanitaire', 'specialite_grade'])
            ->toJson();
    }



    public function delete($id)
    {
        Fonctionnaire::where('id', $id)->delete();
        return redirect()->back()->with('success', 'Fonctionnaire supprimé avec succès');
    }

    public function export()
    {
        $data = Fonctionnaire::with([
            'fonction',
            'nomFormationSanitaire',
            'service',
            'specialiteGrade',
            'position'
        ])->get();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Column Headers
        $headers = [
            'A1' => 'PPR',
            'B1' => 'Nom',
            'C1' => 'Nom Arabe',
            'D1' => 'Prenom',
            'E1' => 'Prenom Arabe',
            'F1' => 'CIN',
            'G1' => 'Sexe',
            'H1' => 'Date Naissance',
            'I1' => 'Date Embauche',
            'J1' => 'Email',
            'K1' => 'Téléphone',
            'L1' => 'Adresse',
            'M1' => 'Fonction',
            'N1' => 'Formation Sanitaire',
            'O1' => 'Remarques',
            'P1' => 'Service',
            'Q1' => 'Spécialité/Grade',
            'R1' => 'Position',
            'S1' => 'Type Mutation',
            'T1' => 'Date Mutation',
            'U1' => 'Date Prise En Service',
            'V1' => 'Categorie Cadre',
            'W1' => 'Cadre',
            'X1' => 'Spécialité',
        ];

        foreach ($headers as $cell => $text) {
            $sheet->setCellValue($cell, $text);
        }

        foreach ($data as $index => $row) {
            $sheet->setCellValue('A' . ($index + 2), $row->ppr);
            $sheet->setCellValue('B' . ($index + 2), $row->nom);
            $sheet->setCellValue('C' . ($index + 2), $row->nom_arabe);
            $sheet->setCellValue('D' . ($index + 2), $row->prenom);
            $sheet->setCellValue('E' . ($index + 2), $row->prenom_arabe);
            $sheet->setCellValue('F' . ($index + 2), $row->cin);
            $sheet->setCellValue('G' . ($index + 2), $row->sexe);
            $sheet->setCellValue('H' . ($index + 2), $row->date_naissance);
            $sheet->setCellValue('I' . ($index + 2), $row->date_embauche);
            $sheet->setCellValue('J' . ($index + 2), $row->email);
            $sheet->setCellValue('K' . ($index + 2), $row->telephone);
            $sheet->setCellValue('L' . ($index + 2), $row->adresse);
            $sheet->setCellValue('M' . ($index + 2), $row->fonction->nom ?? '');
            $sheet->setCellValue('N' . ($index + 2), $row->nomFormationSanitaire->nom ?? '');
            $sheet->setCellValue('O' . ($index + 2), $row->remarques);
            $sheet->setCellValue('P' . ($index + 2), $row->service->nom ?? '');
            $sheet->setCellValue('Q' . ($index + 2), $row->specialiteGrade->nom ?? '');
            $sheet->setCellValue('R' . ($index + 2), $row->position->nom ?? '');
            $sheet->setCellValue('S' . ($index + 2), $row->type_mutation);
            $sheet->setCellValue('T' . ($index + 2), $row->date_mutation);
            $sheet->setCellValue('U' . ($index + 2), $row->date_prise_en_service);
            $sheet->setCellValue('V' . ($index + 2), $row->specialiteGrade->grade->cadre->categorie_cadre->nom);
            $sheet->setCellValue('W' . ($index + 2), $row->specialiteGrade->grade->cadre->nom);
            $sheet->setCellValue('X' . ($index + 2), $row->specialiteGrade->nom);
        }

        $fileName = 'Fonctionnaire.xlsx';
        $filePath = storage_path("app/public/{$fileName}");

        $writer = new Xlsx($spreadsheet);
        $writer->save($filePath);

        return response()->download($filePath)->deleteFileAfterSend();
    }

    public function pdf()
    {
        $data = Fonctionnaire::with([
            'fonction',
            'nomFormationSanitaire',
            'service',
            'specialiteGrade',
            'position'
        ])->get();

        $pdf = new CustomPDF('L', 'mm', 'A4'); // 'L' for Landscape

        // Set document information
        $pdf->SetCreator('GRHDMSP-Fes');
        $pdf->SetAuthor('Med Kaddouri');
        $pdf->SetTitle('Fonctionnaires');
        $pdf->SetSubject('Export PDF');

        // Set header and footer images
        $pdf->headerImage = public_path('html/assets/img/pdf/header-delegation-paysage.png');
        // $pdf->footerImage = public_path('html/assets/img/pdf/footer-deligation.png');

        // Set margins
        $pdf->SetMargins(15, 50, 15); // Left, Top, Right
        $pdf->SetHeaderMargin(20);
        $pdf->SetFooterMargin(20);

        // Set Auto Page Break
        $pdf->SetAutoPageBreak(true, 30); // Bottom margin for auto page break

        // Add a page
        $pdf->AddPage();

        // Set font
        $pdf->SetFont('dejavusans', '', 12);

        $html = '<h3 style="text-align: center; margin-bottom: 5px;">Liste des Fonctionnaires</h3>';
        $html .= '<table border="1" cellspacing="0" cellpadding="1.5" style="width: 100%; text-align: center; border-collapse: collapse; margin-top: 10px;">
                    <thead>
                        <tr>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">PPR</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">Nom</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">Nom Arabe</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">Prénom</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">Prénom Arabe</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">CIN</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">Sexe</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">Date Naissance</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">Date Embauche</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">Email</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">Téléphone</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">Adresse</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">Fonction</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">Service</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">Position</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">Categorie Cadre</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">Cadre</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 7px;">Specialite</th>
                        </tr>
                    </thead>
                    <tbody>';

        foreach ($data as $row) {
            $html .= '<tr>
                        <td style="font-size: 5px;font-weight:700;">' . $row->ppr . '</td>
                        <td style="font-size: 5px;font-weight:700;">' . $row->nom . '</td>
                        <td style="direction: rtl; text-align: center; font-size: 5px;font-weight:700;">' . $row->nom_arabe . '</td>
                        <td style="font-size: 5px;font-weight:700;">' . $row->prenom . '</td>
                        <td style="direction: rtl; text-align: center; font-size: 5px;font-weight:700;">' . $row->prenom_arabe . '</td>
                        <td style="font-size: 5px;font-weight:700;">' . $row->cin . '</td>
                        <td style="font-size: 5px;font-weight:700;">' . $row->sexe . '</td>
                        <td style="font-size: 5px;font-weight:700;">' . $row->date_naissance . '</td>
                        <td style="font-size: 5px;font-weight:700;">' . $row->date_embauche . '</td>
                        <td style="font-size: 5px;font-weight:700;">' . $row->email . '</td>
                        <td style="font-size: 5px;font-weight:700;">' . $row->telephone . '</td>
                        <td style="font-size: 5px;font-weight:700;">' . $row->adresse . '</td>
                        <td style="font-size: 5px;font-weight:700;">' . ($row->fonction->nom ?? '') . '</td>
                        <td style="font-size: 5px;font-weight:700;">' . ($row->service->nom ?? '') . '</td>
                        <td style="font-size: 5px;font-weight:700;">' . ($row->position->nom ?? '') . '</td>
                        <td style="font-size: 5px;font-weight:700;">' . ($row->specialiteGrade->grade->cadre->categorie_cadre->nom ?? '') . '</td>
                        <td style="font-size: 5px;font-weight:700;">' . ($row->specialiteGrade->grade->cadre->nom ?? '') . '</td>
                        <td style="font-size: 5px;font-weight:700;">' . ($row->specialiteGrade->nom ?? '') . '</td>
                      </tr>';
        }

        $html .= '</tbody></table>';

        // Write HTML content
        $pdf->writeHTML($html, true, false, true, false, '');

        // Output PDF to the browser
        return response()->streamDownload(
            fn() => $pdf->Output('Type_de_mutation.pdf', 'I'),
            'Type_de_mutation.pdf'
        );
    }


    /**
     * Export the list of latest assignments to Excel
     */
    public function exportAssignments(Request $request)
    {
        // Get parameters from request
        $date = $request->input('export_date', date('Y-m-d'));
        $formationSanitaireId = $request->input('formation_sanitaire_id');

        // Query to get the latest assignments
        $query = Fonctionnaire::with([
            'fonction',
            'nomFormationSanitaire',
            'service',
            'specialiteGrade',
            'position',
            'historiqueMutation' => function($q) {
                $q->orderBy('date_mutation', 'desc');
            }
        ]);

        // Filter by formation sanitaire if provided
        if ($formationSanitaireId) {
            $query->where('nom_formation_sanitaire_id', $formationSanitaireId);
        }

        $fonctionnaires = $query->get();

        // Create spreadsheet
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle('Affectations');

        // Set headers
        $headers = [
            'A1' => 'PPR',
            'B1' => 'CIN',
            'C1' => 'Nom et Prénom',
            'D1' => 'Nom et Prénom (Arabe)',
            'E1' => 'Formation Sanitaire',
            'F1' => 'Service',
            'G1' => 'Fonction',
            'H1' => 'Spécialité/Grade',
            'I1' => 'Date d\'affectation',
            'J1' => 'Type de mutation',
            'K1' => 'Date de prise en service',
        ];

        // Apply header styles
        foreach ($headers as $cell => $text) {
            $sheet->setCellValue($cell, $text);
            $sheet->getStyle($cell)->getFont()->setBold(true);
            $sheet->getStyle($cell)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
            $sheet->getStyle($cell)->getFill()->getStartColor()->setRGB('4b75ff');
            $sheet->getStyle($cell)->getFont()->getColor()->setRGB('FFFFFF');
            $sheet->getStyle($cell)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
        }

        // Fill data
        $row = 2;
        foreach ($fonctionnaires as $fonctionnaire) {
            // Check if there's a mutation history
            $latestMutation = $fonctionnaire->historiqueMutation->first();

            // Use mutation data if available, otherwise use fonctionnaire data
            $dateAffectation = $latestMutation ? $latestMutation->date_mutation : $fonctionnaire->date_mutation;
            $typeMutation = $latestMutation ?
                ($latestMutation->typeMutation ? $latestMutation->typeMutation->nom : '-') :
                ($fonctionnaire->typeMutation ? $fonctionnaire->typeMutation->nom : '-');
            $datePriseService = $latestMutation ? $latestMutation->date_prise_en_service : $fonctionnaire->date_prise_en_service;

            // Skip if the date is after the requested date
            if ($dateAffectation && Carbon::parse($dateAffectation)->isAfter(Carbon::parse($date))) {
                continue;
            }

            $sheet->setCellValue('A' . $row, $fonctionnaire->ppr);
            $sheet->setCellValue('B' . $row, $fonctionnaire->cin);
            $sheet->setCellValue('C' . $row, $fonctionnaire->nom . ' ' . $fonctionnaire->prenom);
            $sheet->setCellValue('D' . $row, $fonctionnaire->nom_arabe . ' ' . $fonctionnaire->prenom_arabe);
            $sheet->setCellValue('E' . $row, $fonctionnaire->nomFormationSanitaire ? $fonctionnaire->nomFormationSanitaire->nom : '-');
            $sheet->setCellValue('F' . $row, $fonctionnaire->service ? $fonctionnaire->service->nom : '-');
            $sheet->setCellValue('G' . $row, $fonctionnaire->fonction ? $fonctionnaire->fonction->nom : '-');
            $sheet->setCellValue('H' . $row, $fonctionnaire->specialiteGrade ? $fonctionnaire->specialiteGrade->nom : '-');
            $sheet->setCellValue('I' . $row, $dateAffectation ?: '-');
            $sheet->setCellValue('J' . $row, $typeMutation);
            $sheet->setCellValue('K' . $row, $datePriseService ?: '-');

            // Apply row styling
            if ($row % 2 == 0) {
                $sheet->getStyle('A' . $row . ':K' . $row)->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID);
                $sheet->getStyle('A' . $row . ':K' . $row)->getFill()->getStartColor()->setRGB('f8f9fa');
            }

            $row++;
        }

        // Auto-size columns
        foreach (range('A', 'K') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Add borders to all cells
        $styleArray = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['rgb' => '000000'],
                ],
            ],
        ];
        $sheet->getStyle('A1:K' . ($row - 1))->applyFromArray($styleArray);

        // Create title for the export
        $formationName = '';
        if ($formationSanitaireId) {
            $formation = NomFormationSanitaire::find($formationSanitaireId);
            if ($formation) {
                $formationName = ' - ' . $formation->nom;
            }
        }

        $fileName = 'Affectations_' . Carbon::parse($date)->format('d-m-Y') . $formationName . '.xlsx';
        $filePath = storage_path("app/public/{$fileName}");

        $writer = new Xlsx($spreadsheet);
        $writer->save($filePath);

        return response()->download($filePath)->deleteFileAfterSend();
    }

    /**
     * Export the list of latest assignments to Word
     */
    public function exportAssignmentsWord(Request $request)
    {
        // Get parameters from request
        $date = $request->input('export_date', date('Y-m-d'));
        $formationSanitaireId = $request->input('formation_sanitaire_id');

        // Query to get the latest assignments
        $query = Fonctionnaire::with([
            'fonction',
            'nomFormationSanitaire',
            'service',
            'specialiteGrade',
            'position',
            'historiqueMutation' => function($q) {
                $q->orderBy('date_mutation', 'desc');
            }
        ]);

        // Filter by formation sanitaire if provided
        if ($formationSanitaireId) {
            $query->where('nom_formation_sanitaire_id', $formationSanitaireId);
        }

        $fonctionnaires = $query->get();

        // Create PDF instead of Word document since PhpWord is causing issues
        $pdf = new CustomPDF('L', 'mm', 'A4'); // 'L' for Landscape

        // Set document information
        $pdf->SetCreator('GRHDMSP-Fes');
        $pdf->SetAuthor('Med Kaddouri');
        $pdf->SetTitle('Liste des Affectations');
        $pdf->SetSubject('Export PDF');

        // Set header and footer images
        $pdf->headerImage = public_path('html/assets/img/pdf/header-delegation-paysage.png');
        $pdf->footerImage = public_path('html/assets/img/pdf/footer-deligation.png');

        // Set margins
        $pdf->SetMargins(15, 50, 15); // Left, Top, Right
        $pdf->SetHeaderMargin(20);
        $pdf->SetFooterMargin(20);

        // Set Auto Page Break
        $pdf->SetAutoPageBreak(true, 30); // Bottom margin for auto page break

        // Add a page
        $pdf->AddPage();

        // Set font
        $pdf->SetFont('dejavusans', '', 12);

        // Add title
        $formationName = '';
        if ($formationSanitaireId) {
            $formation = NomFormationSanitaire::find($formationSanitaireId);
            if ($formation) {
                $formationName = ' - ' . $formation->nom;
            }
        }

        $title = 'Liste des Affectations au ' . Carbon::parse($date)->format('d/m/Y') . $formationName;

        $html = '<h3 style="text-align: center; margin-bottom: 15px;">' . $title . '</h3>';
        $html .= '<table border="1" cellspacing="0" cellpadding="3" style="width: 100%; text-align: center; border-collapse: collapse; margin-top: 10px;">
                    <thead>
                        <tr>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 10px;">PPR</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 10px;">CIN</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 10px;">Nom et Prénom</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 10px;">Nom et Prénom (Arabe)</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 10px;">Formation Sanitaire</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 10px;">Service</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 10px;">Fonction</th>
                            <th style="background-color: #4b75ff; color: white; font-weight: bold; font-size: 10px;">Date d\'affectation</th>
                        </tr>
                    </thead>
                    <tbody>';

        $rowCount = 0;
        foreach ($fonctionnaires as $fonctionnaire) {
            // Check if there's a mutation history
            $latestMutation = $fonctionnaire->historiqueMutation->first();

            // Use mutation data if available, otherwise use fonctionnaire data
            $dateAffectation = $latestMutation ? $latestMutation->date_mutation : $fonctionnaire->date_mutation;

            // Skip if the date is after the requested date
            if ($dateAffectation && Carbon::parse($dateAffectation)->isAfter(Carbon::parse($date))) {
                continue;
            }

            $rowStyle = $rowCount % 2 == 0 ? 'background-color: #f8f9fa;' : '';
            $rowCount++;

            $html .= '<tr style="' . $rowStyle . '">
                        <td style="font-size: 9px;">' . ($fonctionnaire->ppr ?: '-') . '</td>
                        <td style="font-size: 9px;">' . ($fonctionnaire->cin ?: '-') . '</td>
                        <td style="font-size: 9px;">' . $fonctionnaire->nom . ' ' . $fonctionnaire->prenom . '</td>
                        <td style="font-size: 9px; direction: rtl;">' . $fonctionnaire->nom_arabe . ' ' . $fonctionnaire->prenom_arabe . '</td>
                        <td style="font-size: 9px;">' . ($fonctionnaire->nomFormationSanitaire ? $fonctionnaire->nomFormationSanitaire->nom : '-') . '</td>
                        <td style="font-size: 9px;">' . ($fonctionnaire->service ? $fonctionnaire->service->nom : '-') . '</td>
                        <td style="font-size: 9px;">' . ($fonctionnaire->fonction ? $fonctionnaire->fonction->nom : '-') . '</td>
                        <td style="font-size: 9px;">' . ($dateAffectation ? Carbon::parse($dateAffectation)->format('d/m/Y') : '-') . '</td>
                      </tr>';
        }

        $html .= '</tbody></table>';

        // Write HTML content
        $pdf->writeHTML($html, true, false, true, false, '');

        // Output PDF to the browser
        $fileName = 'Affectations_' . Carbon::parse($date)->format('d-m-Y') . $formationName . '.pdf';
        $filePath = storage_path("app/public/{$fileName}");

        $pdf->Output($filePath, 'F');

        return response()->download($filePath)->deleteFileAfterSend();
    }

    public function attestation($id)
    {
        $fonctionnaire  = Fonctionnaire::findOrFail($id);


        $pdf = new CustomPDF();

        // Set document information
        $pdf->SetCreator('GRHDMSP-Fes');
        $pdf->SetAuthor('Med Kaddouri');
        $pdf->SetTitle('Attestation de Travail');
        $pdf->SetSubject('Export PDF');

        // Set header and footer images
        $pdf->headerImage = public_path('html/assets/img/pdf/header-delegation-urh.png');
        $pdf->footerImage = public_path('html/assets/img/pdf/footer-deligation.png');

        // Set margins
        $pdf->SetMargins(15, 50, 15); // Left, Top, Right
        $pdf->SetHeaderMargin(20);
        $pdf->SetFooterMargin(20);

        // Set Auto Page Break
        $pdf->SetAutoPageBreak(true, 30); // Bottom margin for auto page break

        // Add a page
        $pdf->AddPage();

        // Set font
        $pdf->SetFont('dejavusans', '', 12);

        // Add content
        $html = '<h2 style="text-align: center; margin-bottom: 20px;">Attestation de Travail</h2>';
        $html .= '<p style="margin-bottom: 20px;"> </p>';
        $html .= '<p style="margin-bottom: 20px;"> </p>';

        $html .= '<p style="margin-bottom: 20px;"><strong>Mr</strong> Le Délégué du Ministére de la Santé et de la Protection Sociale à la préfecture de Fès, atteste que: </p>';
        $html .= '<p style="margin-bottom: 20px;"> </p>';
        $html .= '<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">';
        $html .= '<tr><td style="padding: 8px; font-weight: bold;">Nom et prénom:</td><td style="padding: 8px;">' . $fonctionnaire->nom . ' ' . $fonctionnaire->prenom . '</td></tr>';
        $html .= '<tr><td style="padding: 8px; font-weight: bold;">Grade:</td><td style="padding: 8px;">' . $fonctionnaire->specialiteGrade->grade->cadre->nom . ' De ' . $fonctionnaire->specialiteGrade->grade->nom . '</td></tr>';
        $html .= '<tr><td style="padding: 8px; font-weight: bold;">Spécialité:</td><td style="padding: 8px;">' . $fonctionnaire->specialiteGrade->nom . '</td></tr>';
        $html .= '<tr><td style="padding: 8px; font-weight: bold;">P.P.R:</td><td style="padding: 8px;">' . $fonctionnaire->ppr . '</td></tr>';
        $html .= '<tr><td style="padding: 8px; font-weight: bold;">Date de recrutement:</td><td style="padding: 8px;">' . \Carbon\Carbon::parse($fonctionnaire->date_embauche)->format('d/m/Y') . '</td></tr>';
        $html .= '<tr><td style="padding: 8px; font-weight: bold;">Est en fonction à:</td><td style="padding: 8px;">' . $fonctionnaire->nomFormationSanitaire->niveaucategorieformation->nom . ' ' . $fonctionnaire->nomFormationSanitaire->nom . '</td></tr>';
        $html .= '</table>';


        $html .= '<p style="margin-bottom: 20px;"> </p>';
        $html .= '<p style="margin-bottom: 20px;"> </p>';

        $html .= '<p style="margin-bottom: 20px;">Attestation délivrée à l\'intersessé(e) sur sa demande pour servir et valoir ce de droit. </p>';
        $html .= '<p style="margin-bottom: 20px;"> </p>';
        $html .= '<p style="margin-bottom: 20px;"> </p>';
        $html .= '<p style="margin-bottom: 20px;"> </p>';
        $html .= '<p style="margin-bottom: 20px;"> </p>';

        $html .= '<p style="margin-bottom: 20px; text-align: right">Fès, le ' . date('d/m/Y') . '</p>';


        // Write HTML content
        $pdf->writeHTML($html, true, false, true, false, '');

        // Output PDF to the browser
        return response()->streamDownload(
            fn() => $pdf->Output('attestation_de_travail' . $fonctionnaire->ppr . '.pdf', 'I'),
            'attestation_de_travail' . $fonctionnaire->ppr . '.pdf'
        );
    }






    public function show($id)
    {
        $fonctionnaire  = Fonctionnaire::findOrFail($id);
        return view('fonctionnaire.show', compact('fonctionnaire'));
    }

    /**
     * Export congés data to Excel
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportConges(Request $request)
    {
        // Validate request
        $request->validate([
            'date_debut' => 'nullable|date',
            'date_fin' => 'nullable|date',
            'type_conge_id' => 'nullable|exists:type_conges,id',
            'formation_sanitaire_id' => 'nullable|exists:nom_formation_sanitaires,id',
            'ppr' => 'nullable|string',
            'format' => 'nullable|in:pdf,excel',
        ]);

        // Build query
        $query = Relicat::with(['fonctionnaire', 'fonctionnaire.nomFormationSanitaire', 'typeConge']);

        // Apply filters
        if ($request->filled('date_debut')) {
            $query->whereDate('date_conge', '>=', $request->date_debut);
        }

        if ($request->filled('date_fin')) {
            $query->whereDate('date_conge', '<=', $request->date_fin);
        }

        if ($request->filled('type_conge_id')) {
            $query->where('type_conge_id', $request->type_conge_id);
        }

        if ($request->filled('formation_sanitaire_id')) {
            $query->whereHas('fonctionnaire', function ($q) use ($request) {
                $q->where('nom_formation_sanitaire_id', $request->formation_sanitaire_id);
            });
        }

        if ($request->filled('ppr')) {
            $query->whereHas('fonctionnaire', function ($q) use ($request) {
                $q->where('ppr', 'like', '%' . $request->ppr . '%');
            });
        }

        // Get data
        $data = $query->get();

        // Check format and generate appropriate export
        $format = $request->input('format', 'excel'); // Default to excel

        if ($format === 'pdf') {
            return $this->generateCongesPDF($data, $request);
        } else {
            return $this->generateCongesExcel($data, $request);
        }
    }

    /**
     * Generate Excel export for congés
     */
    private function generateCongesExcel($data, $request)
    {
        // Create Excel file
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set headers
        $sheet->setCellValue('A1', 'Nom & Prénom');
        $sheet->setCellValue('B1', 'Affectation');
        $sheet->setCellValue('C1', 'PPR');
        $sheet->setCellValue('D1', 'Type de congé');
        $sheet->setCellValue('E1', 'Nombre de jours de congé');
        $sheet->setCellValue('F1', 'Date de prise');

        // Style headers
        $headerStyle = $sheet->getStyle('A1:F1');
        $headerStyle->getFont()->setBold(true);
        $headerStyle->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setARGB('FFCCCCCC');

        // Add data
        $row = 2;
        foreach ($data as $item) {
            if (!$item->fonctionnaire) {
                continue; // Skip if fonctionnaire is null
            }

            $sheet->setCellValue('A' . $row, $item->fonctionnaire->nom . ' ' . $item->fonctionnaire->prenom);
            $sheet->setCellValue('B' . $row, $item->fonctionnaire->nomFormationSanitaire ? $item->fonctionnaire->nomFormationSanitaire->nom : '');
            $sheet->setCellValue('C' . $row, $item->fonctionnaire->ppr);
            $sheet->setCellValue('D' . $row, $item->typeConge ? $item->typeConge->nom : '');
            $sheet->setCellValue('E' . $row, $item->nbr_jours_disponibles);
            $sheet->setCellValue('F' . $row, $item->date_conge ? \Carbon\Carbon::parse($item->date_conge)->format('d/m/Y') : '');

            $row++;
        }

        // Auto size columns
        foreach (range('A', 'F') as $column) {
            $sheet->getColumnDimension($column)->setAutoSize(true);
        }

        // Create file
        $fileName = 'Congés_' . date('Y-m-d_H-i-s') . '.xlsx';
        $filePath = storage_path("app/public/{$fileName}");

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($filePath);

        return response()->download($filePath)->deleteFileAfterSend();
    }

    /**
     * Generate PDF export for congés
     */
    private function generateCongesPDF($data, $request)
    {
        $pdf = new CustomPDF();

        // Set document information
        $pdf->SetCreator('Application GRH-DMSPS Fès');
        $pdf->SetAuthor('DMSPS Fès');
        $pdf->SetTitle('Export des Congés');
        $pdf->SetSubject('Liste des congés');

        // Set header and footer images
        $pdf->headerImage = public_path('html/assets/img/pdf/header-delegation-urh.png');
        $pdf->footerImage = public_path('html/assets/img/pdf/footer-deligation.png');

        // Set margins
        $pdf->SetMargins(15, 50, 15);
        $pdf->SetHeaderMargin(20);
        $pdf->SetFooterMargin(20);
        $pdf->SetAutoPageBreak(true, 30);

        // Add page
        $pdf->AddPage();

        // Title
        $pdf->SetFont('dejavusans', 'B', 16);
        $pdf->Cell(0, 10, 'EXPORT DES CONGÉS', 0, 1, 'C');
        $pdf->Ln(5);

        // Filters info
        $pdf->SetFont('dejavusans', '', 10);
        if ($request->filled('date_debut') || $request->filled('date_fin')) {
            $pdf->Cell(0, 6, 'Période : ' . ($request->date_debut ?? 'Non définie') . ' au ' . ($request->date_fin ?? 'Non définie'), 0, 1, 'L');
        }
        if ($request->filled('ppr')) {
            $pdf->Cell(0, 6, 'PPR : ' . $request->ppr, 0, 1, 'L');
        }
        $pdf->Ln(5);

        // Table header
        $pdf->SetFont('dejavusans', 'B', 9);
        $pdf->SetFillColor(230, 230, 230);
        $pdf->Cell(40, 8, 'Nom & Prénom', 1, 0, 'C', true);
        $pdf->Cell(35, 8, 'PPR', 1, 0, 'C', true);
        $pdf->Cell(40, 8, 'Affectation', 1, 0, 'C', true);
        $pdf->Cell(30, 8, 'Type congé', 1, 0, 'C', true);
        $pdf->Cell(20, 8, 'Jours', 1, 0, 'C', true);
        $pdf->Cell(25, 8, 'Date', 1, 1, 'C', true);

        // Table data
        $pdf->SetFont('dejavusans', '', 8);
        $pdf->SetFillColor(255, 255, 255);

        foreach ($data as $item) {
            if (!$item->fonctionnaire) {
                continue;
            }

            // Check if we need a new page
            if ($pdf->GetY() > 250) {
                $pdf->AddPage();
                // Repeat header
                $pdf->SetFont('dejavusans', 'B', 9);
                $pdf->SetFillColor(230, 230, 230);
                $pdf->Cell(40, 8, 'Nom & Prénom', 1, 0, 'C', true);
                $pdf->Cell(35, 8, 'PPR', 1, 0, 'C', true);
                $pdf->Cell(40, 8, 'Affectation', 1, 0, 'C', true);
                $pdf->Cell(30, 8, 'Type congé', 1, 0, 'C', true);
                $pdf->Cell(20, 8, 'Jours', 1, 0, 'C', true);
                $pdf->Cell(25, 8, 'Date', 1, 1, 'C', true);
                $pdf->SetFont('dejavusans', '', 8);
            }

            $pdf->Cell(40, 6, substr($item->fonctionnaire->nom . ' ' . $item->fonctionnaire->prenom, 0, 25), 1, 0, 'L');
            $pdf->Cell(35, 6, $item->fonctionnaire->ppr, 1, 0, 'C');
            $pdf->Cell(40, 6, substr($item->fonctionnaire->nomFormationSanitaire ? $item->fonctionnaire->nomFormationSanitaire->nom : '', 0, 25), 1, 0, 'L');
            $pdf->Cell(30, 6, substr($item->typeConge ? $item->typeConge->nom : '', 0, 20), 1, 0, 'L');
            $pdf->Cell(20, 6, $item->nbr_jours_disponibles, 1, 0, 'C');
            $pdf->Cell(25, 6, $item->date_conge ? \Carbon\Carbon::parse($item->date_conge)->format('d/m/Y') : '', 1, 1, 'C');
        }

        // Footer info
        $pdf->Ln(10);
        $pdf->SetFont('dejavusans', '', 10);
        $pdf->Cell(0, 6, 'Total des enregistrements : ' . count($data), 0, 1, 'L');
        $pdf->Cell(0, 6, 'Document généré le ' . date('d/m/Y à H:i'), 0, 1, 'R');

        // Output PDF
        $filename = 'conges_export_' . date('Y-m-d_H-i-s') . '.pdf';
        return response($pdf->Output($filename, 'S'))
            ->header('Content-Type', 'application/pdf')
            ->header('Content-Disposition', 'inline; filename="' . $filename . '"');
    }

    /**
     * Generate a PDF decision for a congé
     *
     * @param int $relicatId
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\StreamedResponse
     */
    public function congeDecision($relicatId, Request $request)
    {
        // Validate ampliations
        $request->validate([
            'ampliations' => 'nullable|array',
            'ampliations.*' => 'string',
        ]);

        // Get the relicat with related data
        $relicat = Relicat::with([
            'fonctionnaire',
            'fonctionnaire.specialiteGrade',
            'fonctionnaire.nomFormationSanitaire',
            'typeConge'
        ])->findOrFail($relicatId);

        $fonctionnaire = $relicat->fonctionnaire;

        if (!$fonctionnaire) {
            return redirect()->back()->with('error', 'Fonctionnaire non trouvé.');
        }

        // Get the year from the relicat date or use the current year
        $congeYear = $relicat->date_conge
            ? Carbon::parse($relicat->date_conge)->year
            : date('Y');

        // Create PDF
        $pdf = new CustomPDF('P', 'mm', 'A4');

        // Set document information
        $pdf->SetCreator('GRHDMSP-Fes');
        $pdf->SetAuthor('Med Kaddouri');
        $pdf->SetTitle('Décision de Congé');
        $pdf->SetSubject('Décision de Congé');

        // Set header and footer images
        $pdf->headerImage = public_path('html/assets/img/pdf/header-delegation-urh.png');
        // $pdf->footerImage = public_path('html/assets/img/pdf/footer-deligation.png');

        // Set margins
        $pdf->SetMargins(20, 50, 20); // Left, Top, Right
        $pdf->SetHeaderMargin(20);
        $pdf->SetFooterMargin(20);

        // Set Auto Page Break
        $pdf->SetAutoPageBreak(true, 30); // Bottom margin for auto page break

        // Add a page
        $pdf->AddPage();

        // Set font
        $pdf->SetFont('dejavusans', '', 12);

        // Add content
        $html = '<h1 style="text-align: center; font-size: 18px; margin-bottom: 20px;text-decoration: underline;">DECISION</h1>';
        $html .= '<p style="text-align: justify; line-height: 0.5;">Le Délégué du M.S. à la Préfecture de Fès :</p>';
        $html .= '<p style="text-align: justify; line-height: 0.5;">Vu la demande de congé présentée par M. ' . $fonctionnaire->nom . ' ' . $fonctionnaire->prenom . '</p>';
        $html .= '<p style="text-align: justify; line-height: 0.5;">Vu l\'avis favorable de son chef immédiat</p>';
        $html .= '<p style="text-align: justify; line-height: 0.5;">Vu les pièces justificatives des journées travaillées:</p>';

        $html .= '<h2 style="text-align: center; font-size: 14px; margin: 20px 0;text-decoration: underline;">DECIDE:</h2>';
        $html .= '<p style="text-align: justify; line-height: 1.2;"><strong>Article unique : </strong><b style="text-transform: uppercase;">' . ($relicat->typeConge ? $relicat->typeConge->nom : 'Congé') . '</b></p>';
        $html .= '<p style="text-align: justify; line-height: 1.2;"><strong>Une durée de : </strong>' . $relicat->nbr_jours_disponibles . ' Jour(s)</p>';
        $html .= '<p style="text-align: justify; line-height: 1.2;"><strong>Au titre de l\'année : </strong>' . $congeYear . '</p>';
        $html .= '<p style="text-align: justify; line-height: 1.2;"><strong>Accordé à : </strong>' . $fonctionnaire->nom . ' ' . $fonctionnaire->prenom . '</p>';
        $html .= '<p style="text-align: justify; line-height: 1.2;"><strong>PPR : </strong>' . $fonctionnaire->ppr . '</p>';
        $html .= '<p style="text-align: justify; line-height: 1.2;"><strong>Grade : </strong>' . ($fonctionnaire->specialiteGrade ? $fonctionnaire->specialiteGrade->nom : '') . '</p>';
        $html .= '<p style="text-align: justify; line-height: 1.2;"><strong>Est en fonction : </strong>' . ($fonctionnaire->specialiteGrade ? $fonctionnaire->specialiteGrade->nom : '') . ' (' . ($fonctionnaire->nomFormationSanitaire ? $fonctionnaire->nomFormationSanitaire->nom : 'Fès') . ')</p>';

        // Format the date if available
        $dateConge = $relicat->date_conge ? Carbon::parse($relicat->date_conge)->format('d/m/Y') : '........................';
        $html .= '<p style="text-align: justify; line-height: 1.5;"><strong>Pour en bénéficier à compter du : </strong> ' . $dateConge . '</p>';

        $html .= '<p style="text-align: right; margin-top: 30px;">Fès, le ' . date('d/m/Y') . '</p>';
        $html .= '<p style="text-align: right; font-weight: bold;">LE DELEGUE DU MINISTERE DE LA SANTE<br>A LA PREFECTURE DE FES</p>';

        // Add ampliations
        $html .= '<p style="margin-top: 40px;"><strong>AMPLIATIONS:</strong></p>';
        $html .= '<ul>';

        // Add selected ampliations
        if ($request->has('ampliations') && is_array($request->ampliations)) {
            foreach ($request->ampliations as $ampliation) {
                $html .= '<li>' . $ampliation . '</li>';
            }
        }

        // Always add these ampliations
        $html .= '<li>L\'INTERESSE(E)</li>';
        $html .= '<li>ARCHIVES.</li>';
        $html .= '</ul>';

        // Write HTML to PDF
        $pdf->writeHTML($html, true, false, true, false, '');

        // Output PDF to the browser
        return response()->streamDownload(
            fn() => $pdf->Output('decision_conge_' . $fonctionnaire->ppr . '.pdf', 'I'),
            'decision_conge_' . $fonctionnaire->ppr . '.pdf'
        );
    }
}
