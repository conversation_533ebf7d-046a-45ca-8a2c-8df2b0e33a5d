<?php

namespace App\Http\Controllers;

use App\Models\Stage;
use App\Models\Ecole;
use App\Models\Option;
use App\Models\Service;
use App\Models\NomFormationSanitaire;
use App\Models\Encadrant;
use Illuminate\Http\Request;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use App\Utils\CustomPDF;

class StageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('stages.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $ecoles = Ecole::all();
        $options = Option::all();
        $services = Service::all();
        $formationSanitaires = NomFormationSanitaire::all();
        $encadrants = Encadrant::all();

        return view('stages.create', compact('ecoles', 'options', 'services', 'formationSanitaires', 'encadrants'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'nom_prenom' => 'required|string|max:255',
            'cin' => 'required|string|max:255',
            'institut_details' => 'required|string',
            'duree' => 'required|integer|min:1',
            'date_debut' => 'required|date',
            'date_fin' => 'required|date|after:date_debut',
            'hierarchie' => 'required|string|max:255',
            'cadre' => 'required|in:médical,administratif',
            'type_stage' => 'required|in:volontaire,formation',
            'ecole_id' => 'required|exists:ecoles,id',
            'option_id' => 'required|exists:options,id',
            'service_id' => 'required|exists:services,id',
            'formation_sanitaire_id' => 'required|exists:nom_formation_sanitaires,id',
            'encadrant_id' => 'required|exists:encadrants,id',
        ]);

        try {
            Stage::create($request->all());
            return redirect()->route('stages.index')->with('success', 'Stage ajouté avec succès');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Une erreur s\'est produite lors de l\'ajout du stage.');
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $stage = Stage::with(['ecole', 'option', 'service', 'formationSanitaire', 'encadrant'])->findOrFail($id);
        return view('stages.show', compact('stage'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $stage = Stage::findOrFail($id);
        $ecoles = Ecole::all();
        $options = Option::all();
        $services = Service::all();
        $formationSanitaires = NomFormationSanitaire::all();
        $encadrants = Encadrant::all();

        return view('stages.edit', compact('stage', 'ecoles', 'options', 'services', 'formationSanitaires', 'encadrants'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $request->validate([
            'nom_prenom' => 'required|string|max:255',
            'cin' => 'required|string|max:255',
            'institut_details' => 'required|string',
            'duree' => 'required|integer|min:1',
            'date_debut' => 'required|date',
            'date_fin' => 'required|date|after:date_debut',
            'hierarchie' => 'required|string|max:255',
            'cadre' => 'required|in:médical,administratif',
            'type_stage' => 'required|in:volontaire,formation',
            'ecole_id' => 'required|exists:ecoles,id',
            'option_id' => 'required|exists:options,id',
            'service_id' => 'required|exists:services,id',
            'formation_sanitaire_id' => 'required|exists:nom_formation_sanitaires,id',
            'encadrant_id' => 'required|exists:encadrants,id',
        ]);

        try {
            $stage = Stage::findOrFail($id);
            $stage->update($request->all());
            return redirect()->route('stages.index')->with('success', 'Stage mis à jour avec succès');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Une erreur s\'est produite lors de la mise à jour du stage.');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            Stage::findOrFail($id)->delete();
            return redirect()->back()->with('success', 'Stage supprimé avec succès');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Une erreur s\'est produite lors de la suppression du stage.');
        }
    }

    /**
     * Get data for DataTables
     */
    public function data(Request $request)
    {
        $query = Stage::with(['ecole', 'option', 'service', 'formationSanitaire', 'encadrant']);

        return DataTables::of($query)
            ->addColumn('action', function ($data) {
                return view('stages.action', compact('data'))->render();
            })
            ->editColumn('date_debut', function ($data) {
                return $data->date_debut->format('d/m/Y');
            })
            ->editColumn('date_fin', function ($data) {
                return $data->date_fin->format('d/m/Y');
            })
            ->editColumn('statut_attestation', function ($data) {
                $badgeClass = match($data->statut_attestation) {
                    'traité' => 'bg-success',
                    'en cours' => 'bg-warning',
                    'pas en cours' => 'bg-danger',
                    default => 'bg-secondary'
                };
                return '<span class="badge ' . $badgeClass . '">' . $data->statut_attestation . '</span>';
            })
            ->rawColumns(['action', 'statut_attestation'])
            ->toJson();
    }

    /**
     * Export stages to Excel
     */
    public function export()
    {
        $stages = Stage::with(['ecole', 'option', 'service', 'formationSanitaire', 'encadrant'])->get();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // En-têtes
        $headers = [
            'Nom & Prénom', 'CIN', 'École', 'Option', 'Formation Sanitaire', 'Service',
            'Encadrant', 'Date Début', 'Date Fin', 'Durée (jours)', 'Cadre', 'Type',
            'Statut', 'Hiérarchie', 'Avis'
        ];

        $col = 'A';
        foreach ($headers as $header) {
            $sheet->setCellValue($col . '1', $header);
            $col++;
        }

        // Données
        $row = 2;
        foreach ($stages as $stage) {
            $sheet->setCellValue('A' . $row, $stage->nom_prenom);
            $sheet->setCellValue('B' . $row, $stage->cin);
            $sheet->setCellValue('C' . $row, $stage->ecole->nom);
            $sheet->setCellValue('D' . $row, $stage->option->nom);
            $sheet->setCellValue('E' . $row, $stage->formationSanitaire->nom);
            $sheet->setCellValue('F' . $row, $stage->service->nom);
            $sheet->setCellValue('G' . $row, $stage->encadrant->nom);
            $sheet->setCellValue('H' . $row, $stage->date_debut->format('d/m/Y'));
            $sheet->setCellValue('I' . $row, $stage->date_fin->format('d/m/Y'));
            $sheet->setCellValue('J' . $row, $stage->duree);
            $sheet->setCellValue('K' . $row, $stage->cadre);
            $sheet->setCellValue('L' . $row, $stage->type_stage);
            $sheet->setCellValue('M' . $row, $stage->statut_attestation);
            $sheet->setCellValue('N' . $row, $stage->hierarchie);
            $sheet->setCellValue('O' . $row, $stage->avis);
            $row++;
        }

        $fileName = 'stages_' . date('Y-m-d_H-i-s') . '.xlsx';
        $filePath = storage_path("app/public/{$fileName}");

        $writer = new Xlsx($spreadsheet);
        $writer->save($filePath);

        return response()->download($filePath)->deleteFileAfterSend();
    }

    /**
     * Export stages to PDF
     */
    public function pdf()
    {
        $stages = Stage::with(['ecole', 'option', 'service', 'formationSanitaire', 'encadrant'])->get();

        $pdf = new CustomPDF();

        // Set document information
        $pdf->SetCreator('GRHDMSP-Fes');
        $pdf->SetAuthor('Med Kaddouri');
        $pdf->SetTitle('Liste des Stages');
        $pdf->SetSubject('Export PDF');

        // Set header and footer images
        $pdf->headerImage = public_path('html/assets/img/pdf/header-delegation.png');
        $pdf->footerImage = public_path('html/assets/img/pdf/footer-deligation.png');

        // Set margins
        $pdf->SetMargins(15, 50, 15);
        $pdf->SetHeaderMargin(20);
        $pdf->SetFooterMargin(20);

        // Set Auto Page Break
        $pdf->SetAutoPageBreak(true, 30);

        // Add a page
        $pdf->AddPage('L'); // Landscape pour plus d'espace

        // Set font
        $pdf->SetFont('dejavusans', '', 10);

        // Add content
        $html = '<h2 style="text-align: center;">Liste des Demandes de Stage</h2>';
        $html .= '<table border="1" cellspacing="0" cellpadding="3" style="width: 100%; text-align: center; border-collapse: collapse; margin-top: 20px; font-size: 8px;">
                    <thead>
                        <tr style="background-color: #4b75ff; color: white; font-weight: bold;">
                            <th>Nom & Prénom</th>
                            <th>CIN</th>
                            <th>École</th>
                            <th>Option</th>
                            <th>Formation Sanitaire</th>
                            <th>Service</th>
                            <th>Date Début</th>
                            <th>Date Fin</th>
                            <th>Durée</th>
                            <th>Statut</th>
                        </tr>
                    </thead>
                    <tbody>';

        foreach ($stages as $stage) {
            $html .= '<tr>
                        <td>' . $stage->nom_prenom . '</td>
                        <td>' . $stage->cin . '</td>
                        <td>' . $stage->ecole->nom . '</td>
                        <td>' . $stage->option->nom . '</td>
                        <td>' . $stage->formationSanitaire->nom . '</td>
                        <td>' . $stage->service->nom . '</td>
                        <td>' . $stage->date_debut->format('d/m/Y') . '</td>
                        <td>' . $stage->date_fin->format('d/m/Y') . '</td>
                        <td>' . $stage->duree . ' jours</td>
                        <td>' . $stage->statut_attestation . '</td>
                      </tr>';
        }

        $html .= '</tbody></table>';

        // Write HTML content
        $pdf->writeHTML($html, true, false, true, false, '');

        // Output PDF to the browser
        return response()->streamDownload(
            fn() => $pdf->Output('stages.pdf', 'I'),
            'stages.pdf'
        );
    }

    /**
     * Generate attestation de stage PDF
     */
    public function attestation($id)
    {
        $stage = Stage::with(['ecole', 'option', 'service', 'formationSanitaire', 'encadrant'])->findOrFail($id);

        $pdf = new CustomPDF();

        // Set document information
        $pdf->SetCreator('GRHDMSP-Fes');
        $pdf->SetAuthor('Med Kaddouri');
        $pdf->SetTitle('Attestation de Stage');
        $pdf->SetSubject('Export PDF');

        // Set header and footer images
        $pdf->headerImage = public_path('html/assets/img/pdf/header-delegation-urh.png');
        $pdf->footerImage = public_path('html/assets/img/pdf/footer-deligation.png');

        // Set margins
        $pdf->SetMargins(15, 50, 15);
        $pdf->SetHeaderMargin(20);
        $pdf->SetFooterMargin(20);

        // Set Auto Page Break
        $pdf->SetAutoPageBreak(true, 30);

        // Add a page
        $pdf->AddPage();

        // Set font
        $pdf->SetFont('dejavusans', '', 12);

        // Add content - Version texte uniquement, compacte sur une page
        $html = '<div style="text-align: center; margin-bottom: 20px;">
                    <h1 style="font-size: 16px; margin: 0; text-decoration: underline;">ATTESTATION DE STAGE</h1>
                </div>';

        $html .= '<div style="line-height: 1.3; font-size: 11px; text-align: justify;">';

        $html .= '<p style="margin: 15px 0;">
                    Je soussigné(e), <strong>' . $stage->hierarchie . '</strong>, certifie par la présente que <strong>' . strtoupper($stage->nom_prenom) . '</strong>, titulaire de la CIN N° <strong>' . $stage->cin . '</strong>, étudiant(e) en <strong>' . $stage->option->nom . '</strong> à <strong>' . $stage->ecole->nom . '</strong>, a effectué un stage de <strong>' . $stage->duree . ' jours</strong> au sein de notre établissement.
                  </p>';

        $html .= '<p style="margin: 15px 0;">
                    Ce stage s\'est déroulé du <strong>' . $stage->date_debut->format('d/m/Y') . '</strong> au <strong>' . $stage->date_fin->format('d/m/Y') . '</strong> au niveau de <strong>' . $stage->formationSanitaire->nom . '</strong>, service de <strong>' . $stage->service->nom . '</strong>, sous l\'encadrement de <strong>' . $stage->encadrant->nom . '</strong>, <strong>' . $stage->encadrant->fonction . '</strong>.
                  </p>';

        $html .= '<p style="margin: 15px 0;">
                    Il s\'agit d\'un stage de type <strong>' . ucfirst($stage->type_stage) . '</strong> dans le cadre <strong>' . ucfirst($stage->cadre) . '</strong>.
                  </p>';

        if ($stage->avis) {
            $html .= '<p style="margin: 15px 0;">
                        <strong>Avis :</strong> ' . $stage->avis . '
                      </p>';
        }

        $html .= '<p style="margin: 20px 0;">
                    Cette attestation est délivrée à l\'intéressé(e) pour servir et valoir ce que de droit.
                  </p>';

        $html .= '<br><br>';

        $html .= '<div style="text-align: right; margin-top: 30px;">
                    <p style="margin: 5px 0;"><strong>Fait à Fès, le ' . now()->format('d/m/Y') . '</strong></p>
                    <p style="margin: 20px 0;"><strong>' . $stage->hierarchie . '</strong></p>
                    <p style="margin-top: 40px;">Signature et cachet</p>
                  </div>';

        $html .= '</div>';

        // Write HTML content
        $pdf->writeHTML($html, true, false, true, false, '');

        // Output PDF to the browser
        return response()->streamDownload(
            fn() => $pdf->Output('attestation_stage_' . $stage->cin . '.pdf', 'I'),
            'attestation_stage_' . $stage->cin . '.pdf'
        );
    }
}
