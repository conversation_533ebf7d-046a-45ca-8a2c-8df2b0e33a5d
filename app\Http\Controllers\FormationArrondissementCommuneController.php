<?php

namespace App\Http\Controllers;

use App\Models\FormationArrondissementCommune;
use Illuminate\Http\Request;

class FormationArrondissementCommuneController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(FormationArrondissementCommune $formationArrondissementCommune)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(FormationArrondissementCommune $formationArrondissementCommune)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, FormationArrondissementCommune $formationArrondissementCommune)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FormationArrondissementCommune $formationArrondissementCommune)
    {
        //
    }
}
