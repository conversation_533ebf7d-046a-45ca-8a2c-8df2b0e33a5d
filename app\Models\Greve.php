<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Greve extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'fonctionnaire_id',
        'date_debut',
        'date_fin',
        'remarque',
    ];

    public function fonctionnaire()
    {
        return $this->belongsTo(Fonctionnaire::class, 'fonctionnaire_id');
    }

    protected static $logAttributes = ['*'];
    protected static $logName = 'Grève';
    protected static $logOnlyDirty = true;

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*'])
            ->logOnlyDirty()
            ->useLogName('Grève');
    }
} 