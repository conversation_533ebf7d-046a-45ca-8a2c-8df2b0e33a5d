<?php if (isset($component)) { $__componentOriginal0f509fab02c45445826003a1e50db506 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0f509fab02c45445826003a1e50db506 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.head','data' => ['titre' => 'Type de mutation']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('head'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['titre' => 'Type de mutation']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0f509fab02c45445826003a1e50db506)): ?>
<?php $attributes = $__attributesOriginal0f509fab02c45445826003a1e50db506; ?>
<?php unset($__attributesOriginal0f509fab02c45445826003a1e50db506); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0f509fab02c45445826003a1e50db506)): ?>
<?php $component = $__componentOriginal0f509fab02c45445826003a1e50db506; ?>
<?php unset($__componentOriginal0f509fab02c45445826003a1e50db506); ?>
<?php endif; ?>
<?php if (isset($component)) { $__componentOriginalfd1f218809a441e923395fcbf03e4272 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfd1f218809a441e923395fcbf03e4272 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.header','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfd1f218809a441e923395fcbf03e4272)): ?>
<?php $attributes = $__attributesOriginalfd1f218809a441e923395fcbf03e4272; ?>
<?php unset($__attributesOriginalfd1f218809a441e923395fcbf03e4272); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfd1f218809a441e923395fcbf03e4272)): ?>
<?php $component = $__componentOriginalfd1f218809a441e923395fcbf03e4272; ?>
<?php unset($__componentOriginalfd1f218809a441e923395fcbf03e4272); ?>
<?php endif; ?>
<?php if (isset($component)) { $__componentOriginal2880b66d47486b4bfeaf519598a469d6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2880b66d47486b4bfeaf519598a469d6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.sidebar','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2880b66d47486b4bfeaf519598a469d6)): ?>
<?php $attributes = $__attributesOriginal2880b66d47486b4bfeaf519598a469d6; ?>
<?php unset($__attributesOriginal2880b66d47486b4bfeaf519598a469d6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2880b66d47486b4bfeaf519598a469d6)): ?>
<?php $component = $__componentOriginal2880b66d47486b4bfeaf519598a469d6; ?>
<?php unset($__componentOriginal2880b66d47486b4bfeaf519598a469d6); ?>
<?php endif; ?>

<!-- Block Principal -->

<div class="page-wrapper">
    <div class="content">
        <div class="page-header">
            <div class="add-item d-flex">
                <div class="page-title">
                    <h4>Gestion des Type de mutation</h4>
                    <h6 class="text-capitalize text-muted">Paramétrage Type de mutation</h6>
                </div>
            </div>
            <ul class="table-top-head">
                <li>
                    <a href="<?php echo e(route("type-de-mutation.pdf")); ?>" target="_blank"  data-bs-toggle="tooltip" data-bs-placement="top" title="Imprimer on PDF"><img
                            src="/html/assets/img/icons/pdf.svg" alt="img"></a>
                </li>
                <li>
                    <a href="<?php echo e(route("type-de-mutation.export")); ?>" data-bs-toggle="tooltip" data-bs-placement="top" title="Exporter Fichier Excel"><img
                            src="/html/assets/img/icons/excel.svg" alt="img"></a>
                </li>
                <li>
                    <a data-bs-toggle="tooltip" data-bs-placement="top" title="Réduit" id="collapse-header"><i
                            data-feather="chevron-up" class="feather-chevron-up"></i></a>
                </li>
            </ul>
            <div class="page-btn">
                <a href="javascript:void(0)" class="btn btn-added" data-bs-toggle="modal" data-bs-target="#add"><i
                        data-feather="plus-circle" class="me-2"></i> Ajouter</a>
            </div>
        </div>


        <!-- /product list -->
        <div class="card table-list-card">
            <div class="card-body">

                <!-- /Filter -->
                <div class="table-responsive">
                    <table class="table  " id="table" style="width:100%">
                        <thead>
                            <tr>
                                <th>Nom en français</th>
                                <th>الإسم بالعربية</th>
                                <th class="no-sort">Actions</th>
                            </tr>
                        </thead>

                    </table>
                </div>
            </div>
        </div>
        <!-- /product list -->
    </div>
</div>
<!-- Block Principal -->

<!-- ajouter - modal-->
<div class="modal fade" id="add">
    <div class="modal-dialog modal-dialog-centered custom-modal-two">
        <div class="modal-content">
            <div class="page-wrapper-new p-0">
                <div class="content">
                    <div class="modal-header border-0 custom-modal-header">
                        <div class="page-title">
                            <h4>Ajouter un Type de mutation</h4>
                        </div>
                        <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body custom-modal-body">
                        <form action="<?php echo e(route('type-de-mutation.add')); ?>" method="post">
                            <?php echo csrf_field(); ?>
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="mb-3">
                                        <label class="form-label" for="nom">Nom en français</label>
                                        <input type="text" name="nom" id="nom"
                                            placeholder="Nom en français" required class="form-control">
                                    </div>
                                    <div class="mb-3" dir="rtl">
                                        <label class="form-label" for="nom_arabe">الإسم بالعربية</label>
                                        <input type="text" name="nom_arabe" id="nom_arabe"
                                            placeholder="الإسم بالعربية" required class="form-control">
                                    </div>
                                </div>

                            </div>
                            <div class="modal-footer-btn">
                                <button type="button" class="btn btn-cancel me-2"
                                    data-bs-dismiss="modal">Annuler</button>
                                <button type="submit" class="btn btn-submit">Ajouter</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ajouter - modal-->




<script>
    document.addEventListener('DOMContentLoaded', function () {

        // DataTable for #table
        var table = new DataTable('#table', {
            processing: true,
            serverSide: true,
            ajax: {
                url: "<?php echo e(route('type-de-mutation.data')); ?>",
                data: function (d) {
                    d.nom = document.getElementById('nom').value || "<?php echo e(request()->get('nom')); ?>" || '';
                    d.nom_arabe = document.getElementById('nom_arabe').value || "<?php echo e(request()->get('nom_arabe')); ?>" || '';
                }
            },
            columns: [
                {
                    data: 'nom',
                    render: function (data, type, row) {
                        if (type === 'display') {
                            return '<span class="badge badge-md bg-success"> ' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    data: 'nom_arabe',
                    render: function (data, type, row) {
                        if (type === 'display') {
                            return '<span class="badge badge-md bg-primary"> ' + data + '</span>';
                        }
                        return data;
                    }
                },
                {
                    data: 'action',
                    orderable: false,
                    searchable: false
                }
            ],
            order: [
                [1, 'desc']
            ],
            orderCellsTop: true,
            language: {
                url: "/html/assets/js/data-French.json"
            }
        });

        // Filter button
        document.getElementById('filter').addEventListener('click', function () {
            table.draw();
        });

        // Additional DataTable for elements with .datanew class
        if (document.querySelectorAll('.datanew').length > 0) {
            document.querySelectorAll('.datanew').forEach(function (element) {
                new DataTable(element, {
                    bFilter: true,
                    sDom: 'fBtlpi',
                    ordering: true,
                    language: {
                        search: ' ',
                        sLengthMenu: '_MENU_',
                        searchPlaceholder: "Search",
                        info: "_START_ - _END_ of _TOTAL_ items",
                        paginate: {
                            next: ' <i class=" fa fa-angle-right"></i>',
                            previous: '<i class="fa fa-angle-left"></i> '
                        }
                    },
                    initComplete: function (settings, json) {
                        // Reposition the DataTable search box
                        var searchBox = document.querySelector('.dataTables_filter');
                        if (searchBox) {
                            var tableSearch = document.getElementById('tableSearch');
                            var searchInput = document.querySelector('.search-input');
                            if (tableSearch) {
                                tableSearch.appendChild(searchBox);
                            }
                            if (searchInput) {
                                searchInput.appendChild(searchBox);
                            }
                        }
                    }
                });
            });
        }

    });
</script>





<?php if (isset($component)) { $__componentOriginal3256840aff405d62010313ad2de837cf = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3256840aff405d62010313ad2de837cf = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.foot','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('foot'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3256840aff405d62010313ad2de837cf)): ?>
<?php $attributes = $__attributesOriginal3256840aff405d62010313ad2de837cf; ?>
<?php unset($__attributesOriginal3256840aff405d62010313ad2de837cf); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3256840aff405d62010313ad2de837cf)): ?>
<?php $component = $__componentOriginal3256840aff405d62010313ad2de837cf; ?>
<?php unset($__componentOriginal3256840aff405d62010313ad2de837cf); ?>
<?php endif; ?>
<?php /**PATH E:\apprh12062025\resources\views/parameters/type_mutations/page.blade.php ENDPATH**/ ?>