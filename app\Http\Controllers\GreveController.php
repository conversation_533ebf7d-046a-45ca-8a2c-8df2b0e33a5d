<?php

namespace App\Http\Controllers;

use App\Models\Greve;
use App\Models\Fonctionnaire;
use Illuminate\Http\Request;

class GreveController extends Controller
{
    public function index()
    {
        $greves = Greve::with('fonctionnaire')->latest()->paginate(20);
        return view('greves.index', compact('greves'));
    }

    public function create()
    {
        $fonctionnaires = Fonctionnaire::all();
        return view('greves.create', compact('fonctionnaires'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'fonctionnaire_id' => 'required|exists:fonctionnaires,id',
            'date_debut' => 'required|date',
            'date_fin' => 'nullable|date|after_or_equal:date_debut',
            'remarque' => 'nullable|string',
        ]);
        Greve::create($request->all());
        return redirect()->back()->with('success', 'Grève ajoutée avec succès.');
    }

    public function show(Greve $greve)
    {
        return view('greves.show', compact('greve'));
    }

    public function edit(Greve $greve)
    {
        $fonctionnaires = Fonctionnaire::all();
        return view('greves.edit', compact('greve', 'fonctionnaires'));
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'fonctionnaire_id' => 'required|exists:fonctionnaires,id',
            'date_debut' => 'required|date',
            'date_fin' => 'nullable|date|after_or_equal:date_debut',
            'remarque' => 'nullable|string',
        ]);
        Greve::findOrFail($id)->update($request->all());
        return redirect()->back()->with('success', 'Grève mise à jour avec succès.');
    }

    public function destroy($id)
    {
        Greve::findOrFail($id)->delete();
        return redirect()->back()->with('success', 'Grève supprimée avec succès.');
    }
} 