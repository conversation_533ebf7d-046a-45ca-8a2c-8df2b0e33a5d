<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class GradeCadre extends Model
{
    use HasFactory, LogsActivity;


    protected $fillable = [
        'nom',
        'nom_arabe',
        'cadre_id',
    ];



    public function cadre(): \Illuminate\Database\Eloquent\Relations\BelongsTo {
        return $this->belongsTo(Cadre::class, 'cadre_id');
    }

    public function specialite_grades(): \Illuminate\Database\Eloquent\Relations\HasMany {
        return $this->hasMany(SpecialiteGrade::class);
    }

    protected static $logAttributes = ['*'];

    protected static $logName = 'Grade Cadre';

    protected static $logOnlyDirty = true;

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*'])
            ->logOnlyDirty()
            ->useLogName('Grade Cadre');
    }

}
