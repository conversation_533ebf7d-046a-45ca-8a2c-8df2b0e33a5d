<?php if (isset($component)) { $__componentOriginal0f509fab02c45445826003a1e50db506 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0f509fab02c45445826003a1e50db506 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.head','data' => ['titre' => 'Gestion des noms de formations sanitaires']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('head'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['titre' => 'Gestion des noms de formations sanitaires']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0f509fab02c45445826003a1e50db506)): ?>
<?php $attributes = $__attributesOriginal0f509fab02c45445826003a1e50db506; ?>
<?php unset($__attributesOriginal0f509fab02c45445826003a1e50db506); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0f509fab02c45445826003a1e50db506)): ?>
<?php $component = $__componentOriginal0f509fab02c45445826003a1e50db506; ?>
<?php unset($__componentOriginal0f509fab02c45445826003a1e50db506); ?>
<?php endif; ?>
<?php if (isset($component)) { $__componentOriginalfd1f218809a441e923395fcbf03e4272 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfd1f218809a441e923395fcbf03e4272 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.header','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfd1f218809a441e923395fcbf03e4272)): ?>
<?php $attributes = $__attributesOriginalfd1f218809a441e923395fcbf03e4272; ?>
<?php unset($__attributesOriginalfd1f218809a441e923395fcbf03e4272); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfd1f218809a441e923395fcbf03e4272)): ?>
<?php $component = $__componentOriginalfd1f218809a441e923395fcbf03e4272; ?>
<?php unset($__componentOriginalfd1f218809a441e923395fcbf03e4272); ?>
<?php endif; ?>
<?php if (isset($component)) { $__componentOriginal2880b66d47486b4bfeaf519598a469d6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2880b66d47486b4bfeaf519598a469d6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.sidebar','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2880b66d47486b4bfeaf519598a469d6)): ?>
<?php $attributes = $__attributesOriginal2880b66d47486b4bfeaf519598a469d6; ?>
<?php unset($__attributesOriginal2880b66d47486b4bfeaf519598a469d6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2880b66d47486b4bfeaf519598a469d6)): ?>
<?php $component = $__componentOriginal2880b66d47486b4bfeaf519598a469d6; ?>
<?php unset($__componentOriginal2880b66d47486b4bfeaf519598a469d6); ?>
<?php endif; ?>

<!-- Block Principal -->

<div class="page-wrapper">


    <style>
        ::-webkit-scrollbar {
            height: 10px;
            width: 4px;
            border: 1px solid #00758a;
        }
    </style>

    <div class="content">
        <div class="page-header">
            <div class="add-item d-flex">
                <div class="page-title">
                    <h4>Gestion des noms de formations sanitaires</h4>
                    <h6 class="text-capitalize text-muted">Paramétrage noms de formations sanitaires</h6>
                </div>
            </div>
            <ul class="table-top-head">
                <li>
                    <a href="<?php echo e(route('nom-formation-sanitaire.pdf')); ?>" target="_blank" data-bs-toggle="tooltip"
                        data-bs-placement="top" title="Imprimer on PDF"><img src="/html/assets/img/icons/pdf.svg"
                            alt="img"></a>
                </li>
                <li>
                    <a href="<?php echo e(route('nom-formation-sanitaire.export')); ?>" data-bs-toggle="tooltip"
                        data-bs-placement="top" title="Exporter Fichier Excel"><img
                            src="/html/assets/img/icons/excel.svg" alt="img"></a>
                </li>
                <li>
                    <a data-bs-toggle="tooltip" data-bs-placement="top" title="Réduit" id="collapse-header"><i
                            data-feather="chevron-up" class="feather-chevron-up"></i></a>
                </li>
            </ul>
            <div class="page-btn">
                <a href="javascript:void(0)" class="btn btn-added" data-bs-toggle="modal" data-bs-target="#add"><i
                        data-feather="plus-circle" class="me-2"></i> Ajouter</a>
            </div>
        </div>


        <!-- /product list -->
        <div class="card table-list-card">
            <div class="card-body">

                <!-- /Filter -->
                <div class="table-responsive">
                    <table class="table  " id="table" style="width:100%">
                        <thead>
                            <tr>
                                <th>Arrondissement/Commune</th>
                                <th>Niveau de Catégorie de formation</th>
                                <th>Nom en français</th>
                                <th>الإسم بالعربية</th>
                                <th>Poplulation</th>

                                <th class="no-sort">Actions</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
        <!-- /product list -->
    </div>
</div>
<!-- Block Principal -->

<!-- ajouter - modal-->
<div class="modal fade modal-lg" id="add">
    <div class="modal-dialog modal-dialog-centered custom-modal-two">
        <div class="modal-content">
            <div class="page-wrapper-new p-0">
                <div class="content">
                    <div class="modal-header border-0 custom-modal-header">
                        <div class="page-title">
                            <h4>Ajouter nom de formations sanitaires</h4>
                        </div>
                        <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body custom-modal-body modal-lg">
                        <form action="<?php echo e(route('nom-formation-sanitaire.add')); ?>" method="post">
                            <?php echo csrf_field(); ?>
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="mb-3">
                                        <label class="form-label" for="niveau_categorie_formation_id">Niveau de
                                            Catégorie de formation</label>
                                        <select name="niveau_categorie_formation_id" id="niveau_categorie_formation_id"
                                            class="form-select searchable-select" required>
                                            <option value="" disabled selected>Choisir un niveau de Catégorie de
                                                formation
                                            </option>
                                            <?php $__currentLoopData = \App\Models\NiveauCategorieFormation::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($item->id); ?>">
                                                    <?php echo e($item->getniveauFormationAttribute()); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label"
                                            for="arrondissement_commune_id">Arrondissement/Commune</label>
                                        <select name="arrondissement_commune_id" id="arrondissement_commune_id"
                                            class="form-select searchable-select" required>
                                            <option value="" disabled selected>Choisir une arrondissement
                                            </option>
                                            <?php $__currentLoopData = \App\Models\ArrondissementCommune::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($item->id); ?>">
                                                    <?php echo e($item->nom . ' ' . $item->nom_arabe); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label" for="nom">Nom en français</label>
                                        <input type="text" name="nom" id="nom"
                                            placeholder="Nom en français" required class="form-control">
                                    </div>
                                    <div class="mb-3" dir="rtl">
                                        <label class="form-label" for="nom_arabe">الإسم بالعربية</label>
                                        <input type="text" name="nom_arabe" id="nom_arabe"
                                            placeholder="الإسم بالعربية" required class="form-control">
                                    </div>

                                </div>

                            </div>
                            <div class="modal-footer-btn">
                                <button type="button" class="btn btn-cancel me-2"
                                    data-bs-dismiss="modal">Annuler</button>
                                <button type="submit" class="btn btn-submit">Ajouter</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- ajouter - modal-->




<script>
    document.addEventListener('DOMContentLoaded', function() {

        // DataTable for #table
        var table = new DataTable('#table', {
            processing: true,
            serverSide: true,
            ajax: {
                url: "<?php echo e(route('nom-formation-sanitaire.data')); ?>",
                data: function(d) {
                    d.nom = document.getElementById('nom').value || "<?php echo e(request()->get('nom')); ?>" ||
                        '';
                    d.nom_arabe = document.getElementById('nom_arabe').value ||
                        "<?php echo e(request()->get('nom_arabe')); ?>" || '';
                }
            },
            columns: [{
                    data: 'arrondissement_commune',
                    render: function(data, type, row) {
                        if (type === 'display') {
                            return '<span class="badge badge-md bg-dark"> ' + data +
                                '</span>';
                        }
                        return data;
                    }
                }, {
                    data: 'niveau_categorie_formation',
                    render: function(data, type, row) {
                        if (type === 'display') {
                            return '<span class="badge badge-md bg-warning"> ' + data +
                                '</span>';
                        }
                        return data;
                    }
                },
                {
                    data: 'nom',
                    render: function(data, type, row) {
                        if (type === 'display') {
                            return '<span class="badge badge-md bg-success"> ' + data +
                                '</span>';
                        }
                        return data;
                    }
                },
                {
                    data: 'nom_arabe',
                    render: function(data, type, row) {
                        if (type === 'display') {
                            return '<span class="badge badge-md bg-primary"> ' + data +
                                '</span>';
                        }
                        return data;
                    }
                },
                {
                    data: 'population',
                    render: function(data, type, row) {
                        if (type === 'display') {
                            return '<span class="badge badge-md bg-pink"> ' + data +
                                '</span>';
                        }
                        return data;
                    }
                },
                {
                    data: 'action',
                    orderable: false,
                    searchable: false
                }
            ],
            order: [
                [1, 'desc']
            ],
            orderCellsTop: true,
            language: {
                url: "/html/assets/js/data-French.json"
            }
        });

        // Filter button
        document.getElementById('filter').addEventListener('click', function() {
            table.draw();
        });

        // Additional DataTable for elements with .datanew class
        if (document.querySelectorAll('.datanew').length > 0) {
            document.querySelectorAll('.datanew').forEach(function(element) {
                new DataTable(element, {
                    bFilter: true,
                    sDom: 'fBtlpi',
                    ordering: true,
                    language: {
                        search: ' ',
                        sLengthMenu: '_MENU_',
                        searchPlaceholder: "Search",
                        info: "_START_ - _END_ of _TOTAL_ items",
                        paginate: {
                            next: ' <i class=" fa fa-angle-right"></i>',
                            previous: '<i class="fa fa-angle-left"></i> '
                        }
                    },
                    initComplete: function(settings, json) {
                        // Reposition the DataTable search box
                        var searchBox = document.querySelector('.dataTables_filter');
                        if (searchBox) {
                            var tableSearch = document.getElementById('tableSearch');
                            var searchInput = document.querySelector('.search-input');
                            if (tableSearch) {
                                tableSearch.appendChild(searchBox);
                            }
                            if (searchInput) {
                                searchInput.appendChild(searchBox);
                            }
                        }
                    }
                });
            });
        }

    });
</script>


<!--  Choices  -->

<!-- Choices -->





<?php if (isset($component)) { $__componentOriginal3256840aff405d62010313ad2de837cf = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3256840aff405d62010313ad2de837cf = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.foot','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('foot'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3256840aff405d62010313ad2de837cf)): ?>
<?php $attributes = $__attributesOriginal3256840aff405d62010313ad2de837cf; ?>
<?php unset($__attributesOriginal3256840aff405d62010313ad2de837cf); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3256840aff405d62010313ad2de837cf)): ?>
<?php $component = $__componentOriginal3256840aff405d62010313ad2de837cf; ?>
<?php unset($__componentOriginal3256840aff405d62010313ad2de837cf); ?>
<?php endif; ?>
<?php /**PATH E:\apprh12062025\resources\views/parameters/nom-formation-sanitaire/page.blade.php ENDPATH**/ ?>