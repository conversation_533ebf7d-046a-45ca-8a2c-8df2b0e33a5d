<?php

namespace App\Http\Controllers;

use App\Models\EtatPriseCesation;
use Illuminate\Http\Request;

class EtatPriseCesationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(EtatPriseCesation $etatPriseCesation)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(EtatPriseCesation $etatPriseCesation)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, EtatPriseCesation $etatPriseCesation)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(EtatPriseCesation $etatPriseCesation)
    {
        //
    }
}
