<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use App\Models\Relicat;
use App\Models\Certificate;
use App\Models\Greve;

class Fonctionnaire extends Model
{

    use HasFactory, LogsActivity;


    protected $fillable = [
        "ppr",
        "nom",
        "nom_arabe",
        "prenom",
        "prenom_arabe",
        "cin",
        "sexe",
        "date_naissance",
        "date_embauche",
        "email",
        "telephone",
        "adresse",
        "fonction_id",
        "nom_formation_sanitaire_id",
        "remarques",
        "service_id",
        "specialite_grade_id",
        "position_id",
        "type_mutation",
        "date_mutation",
        "date_prise_en_service",
        "date_note",
        "ref_note",
        "fichier_note"
    ];

    public function fonction(): BelongsTo
    {
        return $this->belongsTo(Fonction::class,'fonction_id');
    }

    public function nomFormationSanitaire(): BelongsTo
    {
        return $this->belongsTo(NomFormationSanitaire::class, 'nom_formation_sanitaire_id');
    }

    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class,'service_id');
    }

    public function specialiteGrade(): BelongsTo
    {
        return $this->belongsTo(SpecialiteGrade::class,'specialite_grade_id');
    }

    public function position(): BelongsTo
    {
        return $this->belongsTo(Position::class, 'position_id');
    }

    public function typeMutation(): BelongsTo
    {
        return $this->belongsTo(TypeMutation::class, 'type_mutation');
    }

    public function historiqueMutation(): HasMany
    {
        return $this->hasMany(HistoriqueMutation::class);
    }

    public function historiquePosition(): HasMany
    {
        return $this->hasMany(PositionHistorique::class);
    }

    public function relicats(): HasMany
    {
        return $this->hasMany(Relicat::class);
    }

    public function certificates(): HasMany
    {
        return $this->hasMany(Certificate::class);
    }

    public function congeAvailabilities(): HasMany
    {
        return $this->hasMany(CongeAvailability::class);
    }

    public function greves()
    {
        return $this->hasMany(Greve::class, 'fonctionnaire_id');
    }

    public function retraites(): HasMany
    {
        return $this->hasMany(Retraite::class);
    }

    protected static $logAttributes = ['*'];

    protected static $logName = 'Fonctionnaire';

    protected static $logOnlyDirty = true;

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['*'])
            ->logOnlyDirty()
            ->useLogName('Fonctionnaire');
    }

}
